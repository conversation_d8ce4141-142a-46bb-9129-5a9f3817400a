2025-08-17 17:51:23,378 - 关键帧分析器 - INFO - 运行测试模式，只分析前2张图片
2025-08-17 17:51:23,378 - 关键帧分析器 - INFO - 开始扫描目录: F:\github\aicut_auto\ai-video-splitter\Keyframes
2025-08-17 17:51:23,445 - 关键帧分析器 - INFO - 找到 3221 个关键帧图片文件
2025-08-17 17:51:23,446 - 关键帧分析器 - INFO - 开始按scene编号排序图片文件
2025-08-17 17:51:23,452 - 关键帧分析器 - INFO - 成功排序 3221 个图片文件
2025-08-17 17:51:23,452 - 关键帧分析器 - INFO - Scene编号范围: 1 - 3221
2025-08-17 17:51:23,452 - 关键帧分析器 - INFO - 限制处理图片数量为: 2
2025-08-17 17:51:23,452 - 关键帧分析器 - INFO - 处理图片: scene001 - keyframe_scene001_start.png
2025-08-17 17:51:23,456 - 关键帧分析器 - INFO - 处理图片: scene002 - keyframe_scene002_start.png
2025-08-17 17:51:23,458 - 关键帧分析器 - INFO - 成功准备 2 张图片用于分析
2025-08-17 17:51:23,458 - 关键帧分析器 - INFO - 图片处理和排序功能测试完成
2025-08-17 17:51:23,458 - 关键帧分析器 - INFO - 准备分析的图片:
2025-08-17 17:51:23,458 - 关键帧分析器 - INFO -   - Scene 001: keyframe_scene001_start.png
2025-08-17 17:51:23,458 - 关键帧分析器 - INFO -   - Scene 002: keyframe_scene002_start.png
2025-08-17 17:51:23,458 - 关键帧分析器 - INFO - 
开始调用Gemini Balance API分析图片...
2025-08-17 17:51:23,458 - 关键帧分析器 - INFO - 开始调用Gemini Balance API分析 2 张图片
2025-08-17 17:51:23,460 - 关键帧分析器 - INFO - 正在调用API: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 17:51:31,463 - 关键帧分析器 - INFO - API调用成功，获得分析结果
2025-08-17 17:51:31,463 - 关键帧分析器 - INFO - API分析完成
2025-08-17 17:51:31,473 - 关键帧分析器 - INFO - 分析结果已保存到: F:\github\aicut_auto\keyframe_analysis_20250817_175131.json
2025-08-17 17:51:31,473 - 关键帧分析器 - INFO - 分析完成！结果已保存到: F:\github\aicut_auto\keyframe_analysis_20250817_175131.json
2025-08-17 17:51:31,475 - 关键帧分析器 - INFO - 
分析结果预览:
2025-08-17 17:51:31,475 - 关键帧分析器 - INFO - ==================================================
2025-08-17 17:51:31,475 - 关键帧分析器 - INFO - ```json
{
  "frame_observations": [
    {
      "frame_number": 1,
      "observation": "该帧展示了夜间或黄昏时分，一条笔直的柏油马路的俯瞰视角。马路两侧是茂密的绿色树木，路灯发出光亮照亮路面。路面上有清晰的白色车道线。画面的左侧垂直显示着“滨海大道”的文字，左上角有备案信息“（爱奇艺）网微剧备字（2025）第13509号”。画面整体清晰，没有人物活动。"
    },
    {
      "frame_number": 2,
      "observation": "该帧延续了第一帧的马路场景，但画面呈现出明显的动态模糊效果，表明有快速的运动。一个模糊的人形物体出现在马路中央，似乎正在快速移动，其轮廓因运动而拉伸。马路、车道线和两侧的树木也因运动模糊而变得不清晰。左上角的备案信息也因模糊而难以辨认，但位置与第一帧一致。整体场景从静态转变为动态。"
    }
  ],
  "overall_activity_summary": "视频片段首先展示了夜间滨海大道的静态全景，随后画面变得模糊，显...
2025-08-17 17:51:31,475 - 关键帧分析器 - INFO - ==================================================
2025-08-17 17:52:22,345 - 关键帧分析器 - INFO - 运行测试模式，只分析前2张图片
2025-08-17 17:52:22,345 - 关键帧分析器 - INFO - 开始扫描目录: F:\github\aicut_auto\ai-video-splitter\Keyframes
2025-08-17 17:52:22,351 - 关键帧分析器 - INFO - 找到 3221 个关键帧图片文件
2025-08-17 17:52:22,351 - 关键帧分析器 - INFO - 开始按scene编号排序图片文件
2025-08-17 17:52:22,358 - 关键帧分析器 - INFO - 成功排序 3221 个图片文件
2025-08-17 17:52:22,358 - 关键帧分析器 - INFO - Scene编号范围: 1 - 3221
2025-08-17 17:52:22,358 - 关键帧分析器 - INFO - 限制处理图片数量为: 2
2025-08-17 17:52:22,358 - 关键帧分析器 - INFO - 处理图片: scene001 - keyframe_scene001_start.png
2025-08-17 17:52:22,361 - 关键帧分析器 - INFO - 处理图片: scene002 - keyframe_scene002_start.png
2025-08-17 17:52:22,363 - 关键帧分析器 - INFO - 成功准备 2 张图片用于分析
2025-08-17 17:52:22,363 - 关键帧分析器 - INFO - 图片处理和排序功能测试完成
2025-08-17 17:52:22,364 - 关键帧分析器 - INFO - 准备分析的图片:
2025-08-17 17:52:22,364 - 关键帧分析器 - INFO -   - Scene 001: keyframe_scene001_start.png
2025-08-17 17:52:22,364 - 关键帧分析器 - INFO -   - Scene 002: keyframe_scene002_start.png
2025-08-17 17:52:22,364 - 关键帧分析器 - INFO - 
开始调用Gemini Balance API分析图片...
2025-08-17 17:52:22,364 - 关键帧分析器 - INFO - 开始调用Gemini Balance API分析 2 张图片
2025-08-17 17:52:22,364 - 关键帧分析器 - INFO - 正在调用API: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 17:52:31,412 - 关键帧分析器 - INFO - API调用成功，获得分析结果
2025-08-17 17:52:31,413 - 关键帧分析器 - INFO - API分析完成
2025-08-17 17:52:31,413 - 关键帧分析器 - INFO - 分析结果已保存到: F:\github\aicut_auto\keyframe_analysis_20250817_175231.json
2025-08-17 17:52:31,413 - 关键帧分析器 - INFO - 分析完成！结果已保存到: F:\github\aicut_auto\keyframe_analysis_20250817_175231.json
2025-08-17 17:52:31,414 - 关键帧分析器 - INFO - 
分析结果预览:
2025-08-17 17:52:31,414 - 关键帧分析器 - INFO - ==================================================
2025-08-17 17:52:31,414 - 关键帧分析器 - INFO - ```json
{
  "frame_observations": [
    {
      "frame_number": 1,
      "observation": "第一帧展示了夜间从高空俯瞰的沥青路面，路面有清晰的白色车道线。道路两侧是茂密的绿色树木，并有路灯发出微弱的光。画面左侧竖向显示有白色文字“滨海大道”，左上角有小字标注“（爱奇艺）网微剧备字（2025）第13509号”。画面中没有人或车辆，整体呈现一种宁静的静态景象。"
    },
    {
      "frame_number": 2,
      "observation": "第二帧是同一条道路的俯瞰视角，但画面呈现出明显的运动模糊效果，表明有快速的移动或镜头晃动。画面中央有一个模糊的、暗红色/粉色的物体，形状不规则，可能是一个人或一辆小型交通工具，正在快速移动。道路两侧的树木和路面线条也因运动而变得模糊。左上角的“爱奇艺”文字依然存在，但“滨海大道”的文字已消失，表明画面已进入动态内容阶段。"
    }
  ],
  "overall_activity_summary": "视频片段从一条夜间空旷...
2025-08-17 17:52:31,414 - 关键帧分析器 - INFO - ==================================================
2025-08-17 17:52:58,600 - 关键帧分析器 - INFO - 指定分析图片数量: 3
2025-08-17 17:52:58,600 - 关键帧分析器 - INFO - 开始扫描目录: F:\github\aicut_auto\ai-video-splitter\Keyframes
2025-08-17 17:52:58,606 - 关键帧分析器 - INFO - 找到 3221 个关键帧图片文件
2025-08-17 17:52:58,606 - 关键帧分析器 - INFO - 开始按scene编号排序图片文件
2025-08-17 17:52:58,612 - 关键帧分析器 - INFO - 成功排序 3221 个图片文件
2025-08-17 17:52:58,613 - 关键帧分析器 - INFO - Scene编号范围: 1 - 3221
2025-08-17 17:52:58,613 - 关键帧分析器 - INFO - 限制处理图片数量为: 3
2025-08-17 17:52:58,613 - 关键帧分析器 - INFO - 处理图片: scene001 - keyframe_scene001_start.png
2025-08-17 17:52:58,617 - 关键帧分析器 - INFO - 处理图片: scene002 - keyframe_scene002_start.png
2025-08-17 17:52:58,618 - 关键帧分析器 - INFO - 处理图片: scene003 - keyframe_scene003_start.png
2025-08-17 17:52:58,633 - 关键帧分析器 - INFO - 成功准备 3 张图片用于分析
2025-08-17 17:52:58,633 - 关键帧分析器 - INFO - 图片处理和排序功能测试完成
2025-08-17 17:52:58,633 - 关键帧分析器 - INFO - 准备分析的图片:
2025-08-17 17:52:58,633 - 关键帧分析器 - INFO -   - Scene 001: keyframe_scene001_start.png
2025-08-17 17:52:58,633 - 关键帧分析器 - INFO -   - Scene 002: keyframe_scene002_start.png
2025-08-17 17:52:58,633 - 关键帧分析器 - INFO -   - Scene 003: keyframe_scene003_start.png
2025-08-17 17:52:58,633 - 关键帧分析器 - INFO - 
开始调用Gemini Balance API分析图片...
2025-08-17 17:52:58,633 - 关键帧分析器 - INFO - 开始调用Gemini Balance API分析 3 张图片
2025-08-17 17:52:58,634 - 关键帧分析器 - INFO - 正在调用API: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 17:53:07,046 - 关键帧分析器 - INFO - API调用成功，获得分析结果
2025-08-17 17:53:07,048 - 关键帧分析器 - INFO - API分析完成
2025-08-17 17:53:07,058 - 关键帧分析器 - INFO - 分析结果已保存到: F:\github\aicut_auto\keyframe_analysis_20250817_175307.json
2025-08-17 17:53:07,058 - 关键帧分析器 - INFO - 分析完成！结果已保存到: F:\github\aicut_auto\keyframe_analysis_20250817_175307.json
2025-08-17 17:53:07,058 - 关键帧分析器 - INFO - 
分析结果预览:
2025-08-17 17:53:07,058 - 关键帧分析器 - INFO - ==================================================
2025-08-17 17:53:07,059 - 关键帧分析器 - INFO - ```json
{
  "frame_observations": [
    {
      "frame_number": 1,
      "observation": "该帧展示了夜间或黄昏时分，一条铺设良好的柏油路面的俯瞰图。路面有清晰的白色车道线，两侧是茂密的绿色树木，并有路灯提供照明。画面左侧有中文文字“滨海大道”叠加，指示地点。路面上没有车辆或行人。"
    },
    {
      "frame_number": 2,
      "observation": "该帧显示了与第一帧相同的道路和环境，但画面呈现出强烈的运动模糊效果。路中央有一个模糊的、难以辨认的深色或红色物体，暗示有物体正在高速通过。整个场景因运动而变得模糊不清，无法辨识具体细节，但能感受到动态变化。"
    },
    {
      "frame_number": 3,
      "observation": "该帧清晰地捕捉到一名身穿黑色皮衣、戴着黑色头盔和手套的骑手，正驾驶一辆黑色运动型摩托车在路上行驶。摩托车的前灯明亮地亮着。场景仍然是夜间的道路，两侧有树木，光线较暗，但摩托车和骑...
2025-08-17 17:53:07,059 - 关键帧分析器 - INFO - ==================================================
