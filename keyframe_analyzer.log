2025-08-17 17:51:23,378 - 关键帧分析器 - INFO - 运行测试模式，只分析前2张图片
2025-08-17 17:51:23,378 - 关键帧分析器 - INFO - 开始扫描目录: F:\github\aicut_auto\ai-video-splitter\Keyframes
2025-08-17 17:51:23,445 - 关键帧分析器 - INFO - 找到 3221 个关键帧图片文件
2025-08-17 17:51:23,446 - 关键帧分析器 - INFO - 开始按scene编号排序图片文件
2025-08-17 17:51:23,452 - 关键帧分析器 - INFO - 成功排序 3221 个图片文件
2025-08-17 17:51:23,452 - 关键帧分析器 - INFO - Scene编号范围: 1 - 3221
2025-08-17 17:51:23,452 - 关键帧分析器 - INFO - 限制处理图片数量为: 2
2025-08-17 17:51:23,452 - 关键帧分析器 - INFO - 处理图片: scene001 - keyframe_scene001_start.png
2025-08-17 17:51:23,456 - 关键帧分析器 - INFO - 处理图片: scene002 - keyframe_scene002_start.png
2025-08-17 17:51:23,458 - 关键帧分析器 - INFO - 成功准备 2 张图片用于分析
2025-08-17 17:51:23,458 - 关键帧分析器 - INFO - 图片处理和排序功能测试完成
2025-08-17 17:51:23,458 - 关键帧分析器 - INFO - 准备分析的图片:
2025-08-17 17:51:23,458 - 关键帧分析器 - INFO -   - Scene 001: keyframe_scene001_start.png
2025-08-17 17:51:23,458 - 关键帧分析器 - INFO -   - Scene 002: keyframe_scene002_start.png
2025-08-17 17:51:23,458 - 关键帧分析器 - INFO - 
开始调用Gemini Balance API分析图片...
2025-08-17 17:51:23,458 - 关键帧分析器 - INFO - 开始调用Gemini Balance API分析 2 张图片
2025-08-17 17:51:23,460 - 关键帧分析器 - INFO - 正在调用API: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 17:51:31,463 - 关键帧分析器 - INFO - API调用成功，获得分析结果
2025-08-17 17:51:31,463 - 关键帧分析器 - INFO - API分析完成
2025-08-17 17:51:31,473 - 关键帧分析器 - INFO - 分析结果已保存到: F:\github\aicut_auto\keyframe_analysis_20250817_175131.json
2025-08-17 17:51:31,473 - 关键帧分析器 - INFO - 分析完成！结果已保存到: F:\github\aicut_auto\keyframe_analysis_20250817_175131.json
2025-08-17 17:51:31,475 - 关键帧分析器 - INFO - 
分析结果预览:
2025-08-17 17:51:31,475 - 关键帧分析器 - INFO - ==================================================
2025-08-17 17:51:31,475 - 关键帧分析器 - INFO - ```json
{
  "frame_observations": [
    {
      "frame_number": 1,
      "observation": "该帧展示了夜间或黄昏时分，一条笔直的柏油马路的俯瞰视角。马路两侧是茂密的绿色树木，路灯发出光亮照亮路面。路面上有清晰的白色车道线。画面的左侧垂直显示着“滨海大道”的文字，左上角有备案信息“（爱奇艺）网微剧备字（2025）第13509号”。画面整体清晰，没有人物活动。"
    },
    {
      "frame_number": 2,
      "observation": "该帧延续了第一帧的马路场景，但画面呈现出明显的动态模糊效果，表明有快速的运动。一个模糊的人形物体出现在马路中央，似乎正在快速移动，其轮廓因运动而拉伸。马路、车道线和两侧的树木也因运动模糊而变得不清晰。左上角的备案信息也因模糊而难以辨认，但位置与第一帧一致。整体场景从静态转变为动态。"
    }
  ],
  "overall_activity_summary": "视频片段首先展示了夜间滨海大道的静态全景，随后画面变得模糊，显...
2025-08-17 17:51:31,475 - 关键帧分析器 - INFO - ==================================================
2025-08-17 17:52:22,345 - 关键帧分析器 - INFO - 运行测试模式，只分析前2张图片
2025-08-17 17:52:22,345 - 关键帧分析器 - INFO - 开始扫描目录: F:\github\aicut_auto\ai-video-splitter\Keyframes
2025-08-17 17:52:22,351 - 关键帧分析器 - INFO - 找到 3221 个关键帧图片文件
2025-08-17 17:52:22,351 - 关键帧分析器 - INFO - 开始按scene编号排序图片文件
2025-08-17 17:52:22,358 - 关键帧分析器 - INFO - 成功排序 3221 个图片文件
2025-08-17 17:52:22,358 - 关键帧分析器 - INFO - Scene编号范围: 1 - 3221
2025-08-17 17:52:22,358 - 关键帧分析器 - INFO - 限制处理图片数量为: 2
2025-08-17 17:52:22,358 - 关键帧分析器 - INFO - 处理图片: scene001 - keyframe_scene001_start.png
2025-08-17 17:52:22,361 - 关键帧分析器 - INFO - 处理图片: scene002 - keyframe_scene002_start.png
2025-08-17 17:52:22,363 - 关键帧分析器 - INFO - 成功准备 2 张图片用于分析
2025-08-17 17:52:22,363 - 关键帧分析器 - INFO - 图片处理和排序功能测试完成
2025-08-17 17:52:22,364 - 关键帧分析器 - INFO - 准备分析的图片:
2025-08-17 17:52:22,364 - 关键帧分析器 - INFO -   - Scene 001: keyframe_scene001_start.png
2025-08-17 17:52:22,364 - 关键帧分析器 - INFO -   - Scene 002: keyframe_scene002_start.png
2025-08-17 17:52:22,364 - 关键帧分析器 - INFO - 
开始调用Gemini Balance API分析图片...
2025-08-17 17:52:22,364 - 关键帧分析器 - INFO - 开始调用Gemini Balance API分析 2 张图片
2025-08-17 17:52:22,364 - 关键帧分析器 - INFO - 正在调用API: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 17:52:31,412 - 关键帧分析器 - INFO - API调用成功，获得分析结果
2025-08-17 17:52:31,413 - 关键帧分析器 - INFO - API分析完成
2025-08-17 17:52:31,413 - 关键帧分析器 - INFO - 分析结果已保存到: F:\github\aicut_auto\keyframe_analysis_20250817_175231.json
2025-08-17 17:52:31,413 - 关键帧分析器 - INFO - 分析完成！结果已保存到: F:\github\aicut_auto\keyframe_analysis_20250817_175231.json
2025-08-17 17:52:31,414 - 关键帧分析器 - INFO - 
分析结果预览:
2025-08-17 17:52:31,414 - 关键帧分析器 - INFO - ==================================================
2025-08-17 17:52:31,414 - 关键帧分析器 - INFO - ```json
{
  "frame_observations": [
    {
      "frame_number": 1,
      "observation": "第一帧展示了夜间从高空俯瞰的沥青路面，路面有清晰的白色车道线。道路两侧是茂密的绿色树木，并有路灯发出微弱的光。画面左侧竖向显示有白色文字“滨海大道”，左上角有小字标注“（爱奇艺）网微剧备字（2025）第13509号”。画面中没有人或车辆，整体呈现一种宁静的静态景象。"
    },
    {
      "frame_number": 2,
      "observation": "第二帧是同一条道路的俯瞰视角，但画面呈现出明显的运动模糊效果，表明有快速的移动或镜头晃动。画面中央有一个模糊的、暗红色/粉色的物体，形状不规则，可能是一个人或一辆小型交通工具，正在快速移动。道路两侧的树木和路面线条也因运动而变得模糊。左上角的“爱奇艺”文字依然存在，但“滨海大道”的文字已消失，表明画面已进入动态内容阶段。"
    }
  ],
  "overall_activity_summary": "视频片段从一条夜间空旷...
2025-08-17 17:52:31,414 - 关键帧分析器 - INFO - ==================================================
2025-08-17 17:52:58,600 - 关键帧分析器 - INFO - 指定分析图片数量: 3
2025-08-17 17:52:58,600 - 关键帧分析器 - INFO - 开始扫描目录: F:\github\aicut_auto\ai-video-splitter\Keyframes
2025-08-17 17:52:58,606 - 关键帧分析器 - INFO - 找到 3221 个关键帧图片文件
2025-08-17 17:52:58,606 - 关键帧分析器 - INFO - 开始按scene编号排序图片文件
2025-08-17 17:52:58,612 - 关键帧分析器 - INFO - 成功排序 3221 个图片文件
2025-08-17 17:52:58,613 - 关键帧分析器 - INFO - Scene编号范围: 1 - 3221
2025-08-17 17:52:58,613 - 关键帧分析器 - INFO - 限制处理图片数量为: 3
2025-08-17 17:52:58,613 - 关键帧分析器 - INFO - 处理图片: scene001 - keyframe_scene001_start.png
2025-08-17 17:52:58,617 - 关键帧分析器 - INFO - 处理图片: scene002 - keyframe_scene002_start.png
2025-08-17 17:52:58,618 - 关键帧分析器 - INFO - 处理图片: scene003 - keyframe_scene003_start.png
2025-08-17 17:52:58,633 - 关键帧分析器 - INFO - 成功准备 3 张图片用于分析
2025-08-17 17:52:58,633 - 关键帧分析器 - INFO - 图片处理和排序功能测试完成
2025-08-17 17:52:58,633 - 关键帧分析器 - INFO - 准备分析的图片:
2025-08-17 17:52:58,633 - 关键帧分析器 - INFO -   - Scene 001: keyframe_scene001_start.png
2025-08-17 17:52:58,633 - 关键帧分析器 - INFO -   - Scene 002: keyframe_scene002_start.png
2025-08-17 17:52:58,633 - 关键帧分析器 - INFO -   - Scene 003: keyframe_scene003_start.png
2025-08-17 17:52:58,633 - 关键帧分析器 - INFO - 
开始调用Gemini Balance API分析图片...
2025-08-17 17:52:58,633 - 关键帧分析器 - INFO - 开始调用Gemini Balance API分析 3 张图片
2025-08-17 17:52:58,634 - 关键帧分析器 - INFO - 正在调用API: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 17:53:07,046 - 关键帧分析器 - INFO - API调用成功，获得分析结果
2025-08-17 17:53:07,048 - 关键帧分析器 - INFO - API分析完成
2025-08-17 17:53:07,058 - 关键帧分析器 - INFO - 分析结果已保存到: F:\github\aicut_auto\keyframe_analysis_20250817_175307.json
2025-08-17 17:53:07,058 - 关键帧分析器 - INFO - 分析完成！结果已保存到: F:\github\aicut_auto\keyframe_analysis_20250817_175307.json
2025-08-17 17:53:07,058 - 关键帧分析器 - INFO - 
分析结果预览:
2025-08-17 17:53:07,058 - 关键帧分析器 - INFO - ==================================================
2025-08-17 17:53:07,059 - 关键帧分析器 - INFO - ```json
{
  "frame_observations": [
    {
      "frame_number": 1,
      "observation": "该帧展示了夜间或黄昏时分，一条铺设良好的柏油路面的俯瞰图。路面有清晰的白色车道线，两侧是茂密的绿色树木，并有路灯提供照明。画面左侧有中文文字“滨海大道”叠加，指示地点。路面上没有车辆或行人。"
    },
    {
      "frame_number": 2,
      "observation": "该帧显示了与第一帧相同的道路和环境，但画面呈现出强烈的运动模糊效果。路中央有一个模糊的、难以辨认的深色或红色物体，暗示有物体正在高速通过。整个场景因运动而变得模糊不清，无法辨识具体细节，但能感受到动态变化。"
    },
    {
      "frame_number": 3,
      "observation": "该帧清晰地捕捉到一名身穿黑色皮衣、戴着黑色头盔和手套的骑手，正驾驶一辆黑色运动型摩托车在路上行驶。摩托车的前灯明亮地亮着。场景仍然是夜间的道路，两侧有树木，光线较暗，但摩托车和骑...
2025-08-17 17:53:07,059 - 关键帧分析器 - INFO - ==================================================
2025-08-17 21:34:26,786 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:34:26,786 - 关键帧分析器 - INFO - 视频关键帧分析工具启动
2025-08-17 21:34:26,787 - 关键帧分析器 - INFO - 每批处理图片数量: 2
2025-08-17 21:34:26,787 - 关键帧分析器 - INFO - 最大重试次数: 3
2025-08-17 21:34:26,787 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:34:26,787 - 关键帧分析器 - INFO - 检查scenes_report.json文件...
2025-08-17 21:34:26,827 - 关键帧分析器 - INFO - 成功加载scenes_report.json，包含 3221 个场景
2025-08-17 21:34:26,827 - 关键帧分析器 - INFO - 开始扫描目录: F:\github\aicut_auto\ai-video-splitter\Keyframes
2025-08-17 21:34:26,833 - 关键帧分析器 - INFO - 找到 3221 个关键帧图片文件
2025-08-17 21:34:26,833 - 关键帧分析器 - INFO - 开始按scene编号排序图片文件
2025-08-17 21:34:26,840 - 关键帧分析器 - INFO - 成功排序 3221 个图片文件
2025-08-17 21:34:26,840 - 关键帧分析器 - INFO - Scene编号范围: 1 - 3221
2025-08-17 21:34:26,840 - 关键帧分析器 - INFO - 限制处理图片数量为: 4
2025-08-17 21:34:26,840 - 关键帧分析器 - INFO - 准备处理 4 张图片
2025-08-17 21:34:26,840 - 关键帧分析器 - INFO - 开始分批处理 4 张图片，共 2 批，每批 2 张
2025-08-17 21:34:26,849 - 关键帧分析器 - INFO - 
处理第 1/2 批: scene [1, 2]
2025-08-17 21:34:26,849 - 关键帧分析器 - INFO -   准备图片: scene001 - keyframe_scene001_start.png
2025-08-17 21:34:26,853 - 关键帧分析器 - INFO -   准备图片: scene002 - keyframe_scene002_start.png
2025-08-17 21:34:26,855 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [1, 2]
2025-08-17 21:34:34,656 - 关键帧分析器 - INFO - API响应验证通过，包含2个有效的observation
2025-08-17 21:34:34,656 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:34:34,657 - 关键帧分析器 - WARNING - 未找到scene_id 1 对应的场景
2025-08-17 21:34:34,657 - 关键帧分析器 - WARNING - 未找到scene_id 2 对应的场景
2025-08-17 21:34:34,657 - 关键帧分析器 - INFO - 成功更新 0 个场景的observation
2025-08-17 21:34:34,657 - 关键帧分析器 - ERROR - 第 1 批更新JSON失败
2025-08-17 21:34:34,657 - 关键帧分析器 - INFO - 
处理第 2/2 批: scene [3, 4]
2025-08-17 21:34:34,658 - 关键帧分析器 - INFO -   准备图片: scene003 - keyframe_scene003_start.png
2025-08-17 21:34:34,661 - 关键帧分析器 - INFO -   准备图片: scene004 - keyframe_scene004_start.png
2025-08-17 21:34:34,663 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [3, 4]
2025-08-17 21:34:42,296 - 关键帧分析器 - INFO - API响应验证通过，包含2个有效的observation
2025-08-17 21:34:42,296 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:34:42,297 - 关键帧分析器 - WARNING - 未找到scene_id 3 对应的场景
2025-08-17 21:34:42,298 - 关键帧分析器 - WARNING - 未找到scene_id 4 对应的场景
2025-08-17 21:34:42,298 - 关键帧分析器 - INFO - 成功更新 0 个场景的observation
2025-08-17 21:34:42,298 - 关键帧分析器 - ERROR - 第 2 批更新JSON失败
2025-08-17 21:34:42,298 - 关键帧分析器 - INFO - 
分批处理完成: 成功 0/2 批
2025-08-17 21:34:42,298 - 关键帧分析器 - ERROR - 失败的scene_id: [1, 2, 3, 4]
2025-08-17 21:34:42,299 - 关键帧分析器 - INFO - 
============================================================
2025-08-17 21:34:42,299 - 关键帧分析器 - INFO - 处理完成报告
2025-08-17 21:34:42,299 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:34:42,299 - 关键帧分析器 - INFO - 总图片数量: 4
2025-08-17 21:34:42,299 - 关键帧分析器 - INFO - 成功处理批次: 0
2025-08-17 21:34:42,299 - 关键帧分析器 - ERROR - 失败的scene_id数量: 4
2025-08-17 21:34:42,299 - 关键帧分析器 - ERROR - 失败的scene_id列表: [1, 2, 3, 4]
2025-08-17 21:34:42,299 - 关键帧分析器 - ERROR - 建议检查:
2025-08-17 21:34:42,299 - 关键帧分析器 - ERROR - 1. 网络连接是否正常
2025-08-17 21:34:42,299 - 关键帧分析器 - ERROR - 2. API服务是否可用
2025-08-17 21:34:42,299 - 关键帧分析器 - ERROR - 3. 图片文件是否损坏
2025-08-17 21:34:42,300 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:35:12,792 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:35:12,792 - 关键帧分析器 - INFO - 视频关键帧分析工具启动
2025-08-17 21:35:12,792 - 关键帧分析器 - INFO - 每批处理图片数量: 2
2025-08-17 21:35:12,792 - 关键帧分析器 - INFO - 最大重试次数: 3
2025-08-17 21:35:12,792 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:35:12,793 - 关键帧分析器 - INFO - 检查scenes_report.json文件...
2025-08-17 21:35:12,833 - 关键帧分析器 - INFO - 成功加载scenes_report.json，包含 3221 个场景
2025-08-17 21:35:12,833 - 关键帧分析器 - INFO - 开始扫描目录: F:\github\aicut_auto\ai-video-splitter\Keyframes
2025-08-17 21:35:12,837 - 关键帧分析器 - INFO - 找到 3221 个关键帧图片文件
2025-08-17 21:35:12,839 - 关键帧分析器 - INFO - 开始按scene编号排序图片文件
2025-08-17 21:35:12,845 - 关键帧分析器 - INFO - 成功排序 3221 个图片文件
2025-08-17 21:35:12,845 - 关键帧分析器 - INFO - Scene编号范围: 1 - 3221
2025-08-17 21:35:12,845 - 关键帧分析器 - INFO - 限制处理图片数量为: 4
2025-08-17 21:35:12,845 - 关键帧分析器 - INFO - 准备处理 4 张图片
2025-08-17 21:35:12,845 - 关键帧分析器 - INFO - 开始分批处理 4 张图片，共 2 批，每批 2 张
2025-08-17 21:35:12,845 - 关键帧分析器 - INFO - 
处理第 1/2 批: scene [1, 2]
2025-08-17 21:35:12,845 - 关键帧分析器 - INFO -   准备图片: scene001 - keyframe_scene001_start.png
2025-08-17 21:35:12,849 - 关键帧分析器 - INFO -   准备图片: scene002 - keyframe_scene002_start.png
2025-08-17 21:35:12,851 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [1, 2]
2025-08-17 21:35:20,224 - 关键帧分析器 - INFO - API响应验证通过，包含2个有效的observation
2025-08-17 21:35:20,224 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:35:20,224 - 关键帧分析器 - INFO - 已更新scene_id 1 的observation
2025-08-17 21:35:20,224 - 关键帧分析器 - INFO - 已更新scene_id 2 的observation
2025-08-17 21:35:20,224 - 关键帧分析器 - INFO - 成功更新 2 个场景的observation
2025-08-17 21:35:20,225 - 关键帧分析器 - INFO - 第 1 批处理成功
2025-08-17 21:35:20,225 - 关键帧分析器 - INFO - 
处理第 2/2 批: scene [3, 4]
2025-08-17 21:35:20,225 - 关键帧分析器 - INFO -   准备图片: scene003 - keyframe_scene003_start.png
2025-08-17 21:35:20,228 - 关键帧分析器 - INFO -   准备图片: scene004 - keyframe_scene004_start.png
2025-08-17 21:35:20,230 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [3, 4]
2025-08-17 21:35:28,941 - 关键帧分析器 - INFO - API响应验证通过，包含2个有效的observation
2025-08-17 21:35:28,941 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:35:28,941 - 关键帧分析器 - INFO - 已更新scene_id 3 的observation
2025-08-17 21:35:28,941 - 关键帧分析器 - INFO - 已更新scene_id 4 的observation
2025-08-17 21:35:28,941 - 关键帧分析器 - INFO - 成功更新 2 个场景的observation
2025-08-17 21:35:28,942 - 关键帧分析器 - INFO - 第 2 批处理成功
2025-08-17 21:35:28,942 - 关键帧分析器 - INFO - 
分批处理完成: 成功 2/2 批
2025-08-17 21:35:29,096 - 关键帧分析器 - INFO - 成功保存scenes_report.json: F:\github\aicut_auto\ai-video-splitter\scenes_report.json
2025-08-17 21:35:29,097 - 关键帧分析器 - INFO - scenes_report.json已成功更新
2025-08-17 21:35:29,097 - 关键帧分析器 - INFO - 
============================================================
2025-08-17 21:35:29,097 - 关键帧分析器 - INFO - 处理完成报告
2025-08-17 21:35:29,097 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:35:29,097 - 关键帧分析器 - INFO - 总图片数量: 4
2025-08-17 21:35:29,097 - 关键帧分析器 - INFO - 成功处理批次: 2
2025-08-17 21:35:29,097 - 关键帧分析器 - INFO - 所有图片处理成功！
2025-08-17 21:35:29,097 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:37:59,144 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:37:59,144 - 关键帧分析器 - INFO - 视频关键帧分析工具启动
2025-08-17 21:37:59,144 - 关键帧分析器 - INFO - 每批处理图片数量: 3
2025-08-17 21:37:59,144 - 关键帧分析器 - INFO - 最大重试次数: 3
2025-08-17 21:37:59,144 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:37:59,144 - 关键帧分析器 - INFO - 检查scenes_report.json文件...
2025-08-17 21:37:59,196 - 关键帧分析器 - INFO - 成功加载scenes_report.json，包含 3221 个场景
2025-08-17 21:37:59,196 - 关键帧分析器 - INFO - 开始扫描目录: F:\github\aicut_auto\ai-video-splitter\Keyframes
2025-08-17 21:37:59,201 - 关键帧分析器 - INFO - 找到 3221 个关键帧图片文件
2025-08-17 21:37:59,202 - 关键帧分析器 - INFO - 开始按scene编号排序图片文件
2025-08-17 21:37:59,209 - 关键帧分析器 - INFO - 成功排序 3221 个图片文件
2025-08-17 21:37:59,209 - 关键帧分析器 - INFO - Scene编号范围: 1 - 3221
2025-08-17 21:37:59,209 - 关键帧分析器 - INFO - 限制处理图片数量为: 6
2025-08-17 21:37:59,209 - 关键帧分析器 - INFO - 准备处理 6 张图片
2025-08-17 21:37:59,209 - 关键帧分析器 - INFO - 开始分批处理 6 张图片，共 2 批，每批 3 张
2025-08-17 21:37:59,209 - 关键帧分析器 - INFO - 
处理第 1/2 批: scene [1, 2, 3]
2025-08-17 21:37:59,209 - 关键帧分析器 - INFO -   准备图片: scene001 - keyframe_scene001_start.png
2025-08-17 21:37:59,213 - 关键帧分析器 - INFO -   准备图片: scene002 - keyframe_scene002_start.png
2025-08-17 21:37:59,215 - 关键帧分析器 - INFO -   准备图片: scene003 - keyframe_scene003_start.png
2025-08-17 21:37:59,218 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [1, 2, 3]
2025-08-17 21:38:08,407 - 关键帧分析器 - INFO - API响应验证通过，包含3个有效的observation
2025-08-17 21:38:08,407 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:38:08,407 - 关键帧分析器 - INFO - 已更新scene_id 1 的observation
2025-08-17 21:38:08,408 - 关键帧分析器 - INFO - 已更新scene_id 2 的observation
2025-08-17 21:38:08,408 - 关键帧分析器 - INFO - 已更新scene_id 3 的observation
2025-08-17 21:38:08,408 - 关键帧分析器 - INFO - 成功更新 3 个场景的observation
2025-08-17 21:38:08,408 - 关键帧分析器 - INFO - 第 1 批处理成功
2025-08-17 21:38:08,408 - 关键帧分析器 - INFO - 
处理第 2/2 批: scene [4, 5, 6]
2025-08-17 21:38:08,408 - 关键帧分析器 - INFO -   准备图片: scene004 - keyframe_scene004_start.png
2025-08-17 21:38:08,410 - 关键帧分析器 - INFO -   准备图片: scene005 - keyframe_scene005_start.png
2025-08-17 21:38:08,412 - 关键帧分析器 - INFO -   准备图片: scene006 - keyframe_scene006_start.png
2025-08-17 21:38:08,413 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [4, 5, 6]
2025-08-17 21:38:17,341 - 关键帧分析器 - INFO - API响应验证通过，包含3个有效的observation
2025-08-17 21:38:17,341 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:38:17,342 - 关键帧分析器 - INFO - 已更新scene_id 4 的observation
2025-08-17 21:38:17,342 - 关键帧分析器 - INFO - 已更新scene_id 5 的observation
2025-08-17 21:38:17,342 - 关键帧分析器 - INFO - 已更新scene_id 6 的observation
2025-08-17 21:38:17,342 - 关键帧分析器 - INFO - 成功更新 3 个场景的observation
2025-08-17 21:38:17,342 - 关键帧分析器 - INFO - 第 2 批处理成功
2025-08-17 21:38:17,342 - 关键帧分析器 - INFO - 
分批处理完成: 成功 2/2 批
2025-08-17 21:38:17,489 - 关键帧分析器 - INFO - 成功保存scenes_report.json: F:\github\aicut_auto\ai-video-splitter\scenes_report.json
2025-08-17 21:38:17,489 - 关键帧分析器 - INFO - scenes_report.json已成功更新
2025-08-17 21:38:17,490 - 关键帧分析器 - INFO - 
============================================================
2025-08-17 21:38:17,490 - 关键帧分析器 - INFO - 处理完成报告
2025-08-17 21:38:17,490 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:38:17,490 - 关键帧分析器 - INFO - 总图片数量: 6
2025-08-17 21:38:17,490 - 关键帧分析器 - INFO - 成功处理批次: 2
2025-08-17 21:38:17,490 - 关键帧分析器 - INFO - 所有图片处理成功！
2025-08-17 21:38:17,490 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:45:22,446 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:45:22,446 - 关键帧分析器 - INFO - 视频关键帧分析工具启动
2025-08-17 21:45:22,446 - 关键帧分析器 - INFO - 每批处理图片数量: 3
2025-08-17 21:45:22,446 - 关键帧分析器 - INFO - 最大重试次数: 3
2025-08-17 21:45:22,447 - 关键帧分析器 - INFO - 并行线程数: 2
2025-08-17 21:45:22,447 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:45:22,447 - 关键帧分析器 - INFO - 检查scenes_report.json文件...
2025-08-17 21:45:22,487 - 关键帧分析器 - INFO - 成功加载scenes_report.json，包含 3221 个场景
2025-08-17 21:45:22,487 - 关键帧分析器 - INFO - 开始扫描目录: F:\github\aicut_auto\ai-video-splitter\Keyframes
2025-08-17 21:45:22,492 - 关键帧分析器 - INFO - 找到 3221 个关键帧图片文件
2025-08-17 21:45:22,492 - 关键帧分析器 - INFO - 开始按scene编号排序图片文件
2025-08-17 21:45:22,498 - 关键帧分析器 - INFO - 成功排序 3221 个图片文件
2025-08-17 21:45:22,498 - 关键帧分析器 - INFO - Scene编号范围: 1 - 3221
2025-08-17 21:45:22,499 - 关键帧分析器 - INFO - 限制处理图片数量为: 6
2025-08-17 21:45:22,499 - 关键帧分析器 - INFO - 准备处理 6 张图片
2025-08-17 21:45:22,499 - 关键帧分析器 - INFO - 开始并行分批处理 6 张图片，共 2 批，每批 3 张
2025-08-17 21:45:22,499 - 关键帧分析器 - INFO - 使用 2 个并行线程
2025-08-17 21:45:22,499 - 关键帧分析器 - INFO - 线程处理第 1 批: scene [1, 2, 3]
2025-08-17 21:45:22,499 - 关键帧分析器 - INFO - 线程处理第 2 批: scene [4, 5, 6]
2025-08-17 21:45:22,510 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [4, 5, 6]
2025-08-17 21:45:22,512 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [1, 2, 3]
2025-08-17 21:45:31,620 - 关键帧分析器 - INFO - API响应验证通过，包含3个有效的observation
2025-08-17 21:45:31,621 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:45:31,621 - 关键帧分析器 - INFO - 已更新scene_id 1 的observation
2025-08-17 21:45:31,621 - 关键帧分析器 - INFO - 已更新scene_id 2 的observation
2025-08-17 21:45:31,621 - 关键帧分析器 - INFO - 已更新scene_id 3 的observation
2025-08-17 21:45:31,621 - 关键帧分析器 - INFO - 成功更新 3 个场景的observation
2025-08-17 21:45:31,622 - 关键帧分析器 - INFO - 第 1 批处理成功
2025-08-17 21:45:32,599 - 关键帧分析器 - INFO - API响应验证通过，包含3个有效的observation
2025-08-17 21:45:32,599 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:45:32,600 - 关键帧分析器 - INFO - 已更新scene_id 4 的observation
2025-08-17 21:45:32,600 - 关键帧分析器 - INFO - 已更新scene_id 5 的observation
2025-08-17 21:45:32,600 - 关键帧分析器 - INFO - 已更新scene_id 6 的observation
2025-08-17 21:45:32,600 - 关键帧分析器 - INFO - 成功更新 3 个场景的observation
2025-08-17 21:45:32,600 - 关键帧分析器 - INFO - 第 2 批处理成功
2025-08-17 21:45:32,601 - 关键帧分析器 - INFO - 
并行处理完成: 成功 2/2 批
2025-08-17 21:45:32,752 - 关键帧分析器 - INFO - 成功保存scenes_report.json: F:\github\aicut_auto\ai-video-splitter\scenes_report.json
2025-08-17 21:45:32,752 - 关键帧分析器 - INFO - scenes_report.json已成功更新
2025-08-17 21:45:32,752 - 关键帧分析器 - INFO - 
============================================================
2025-08-17 21:45:32,752 - 关键帧分析器 - INFO - 处理完成报告
2025-08-17 21:45:32,752 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:45:32,753 - 关键帧分析器 - INFO - 总图片数量: 6
2025-08-17 21:45:32,753 - 关键帧分析器 - INFO - 成功处理批次: 2
2025-08-17 21:45:32,753 - 关键帧分析器 - INFO - 所有图片处理成功！
2025-08-17 21:45:32,753 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:46:22,616 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:46:22,616 - 关键帧分析器 - INFO - 视频关键帧分析工具启动
2025-08-17 21:46:22,616 - 关键帧分析器 - INFO - 每批处理图片数量: 2
2025-08-17 21:46:22,617 - 关键帧分析器 - INFO - 最大重试次数: 3
2025-08-17 21:46:22,617 - 关键帧分析器 - INFO - 并行线程数: 5
2025-08-17 21:46:22,617 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:46:22,617 - 关键帧分析器 - INFO - 检查scenes_report.json文件...
2025-08-17 21:46:22,657 - 关键帧分析器 - INFO - 成功加载scenes_report.json，包含 3221 个场景
2025-08-17 21:46:22,658 - 关键帧分析器 - INFO - 开始扫描目录: F:\github\aicut_auto\ai-video-splitter\Keyframes
2025-08-17 21:46:22,663 - 关键帧分析器 - INFO - 找到 3221 个关键帧图片文件
2025-08-17 21:46:22,663 - 关键帧分析器 - INFO - 开始按scene编号排序图片文件
2025-08-17 21:46:22,669 - 关键帧分析器 - INFO - 成功排序 3221 个图片文件
2025-08-17 21:46:22,669 - 关键帧分析器 - INFO - Scene编号范围: 1 - 3221
2025-08-17 21:46:22,670 - 关键帧分析器 - INFO - 限制处理图片数量为: 10
2025-08-17 21:46:22,670 - 关键帧分析器 - INFO - 准备处理 10 张图片
2025-08-17 21:46:22,670 - 关键帧分析器 - INFO - 开始并行分批处理 10 张图片，共 5 批，每批 2 张
2025-08-17 21:46:22,670 - 关键帧分析器 - INFO - 使用 5 个并行线程
2025-08-17 21:46:22,670 - 关键帧分析器 - INFO - 线程处理第 1 批: scene [1, 2]
2025-08-17 21:46:22,670 - 关键帧分析器 - INFO - 线程处理第 2 批: scene [3, 4]
2025-08-17 21:46:22,671 - 关键帧分析器 - INFO - 线程处理第 3 批: scene [5, 6]
2025-08-17 21:46:22,671 - 关键帧分析器 - INFO - 线程处理第 4 批: scene [7, 8]
2025-08-17 21:46:22,671 - 关键帧分析器 - INFO - 线程处理第 5 批: scene [9, 10]
2025-08-17 21:46:22,684 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [1, 2]
2025-08-17 21:46:22,688 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [3, 4]
2025-08-17 21:46:22,694 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [9, 10]
2025-08-17 21:46:22,694 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [5, 6]
2025-08-17 21:46:22,694 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [7, 8]
2025-08-17 21:46:30,269 - 关键帧分析器 - INFO - API响应验证通过，包含2个有效的observation
2025-08-17 21:46:30,269 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:46:30,269 - 关键帧分析器 - INFO - 已更新scene_id 5 的observation
2025-08-17 21:46:30,270 - 关键帧分析器 - INFO - 已更新scene_id 6 的observation
2025-08-17 21:46:30,270 - 关键帧分析器 - INFO - 成功更新 2 个场景的observation
2025-08-17 21:46:30,270 - 关键帧分析器 - INFO - 第 3 批处理成功
2025-08-17 21:46:30,460 - 关键帧分析器 - INFO - API响应验证通过，包含2个有效的observation
2025-08-17 21:46:30,460 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:46:30,462 - 关键帧分析器 - INFO - 已更新scene_id 3 的observation
2025-08-17 21:46:30,462 - 关键帧分析器 - INFO - 已更新scene_id 4 的observation
2025-08-17 21:46:30,462 - 关键帧分析器 - INFO - 成功更新 2 个场景的observation
2025-08-17 21:46:30,463 - 关键帧分析器 - INFO - 第 2 批处理成功
2025-08-17 21:46:32,512 - 关键帧分析器 - INFO - API响应验证通过，包含2个有效的observation
2025-08-17 21:46:32,512 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:46:32,514 - 关键帧分析器 - INFO - 已更新scene_id 1 的observation
2025-08-17 21:46:32,514 - 关键帧分析器 - INFO - 已更新scene_id 2 的observation
2025-08-17 21:46:32,514 - 关键帧分析器 - INFO - 成功更新 2 个场景的observation
2025-08-17 21:46:32,514 - 关键帧分析器 - INFO - 第 1 批处理成功
2025-08-17 21:46:32,669 - 关键帧分析器 - INFO - API响应验证通过，包含2个有效的observation
2025-08-17 21:46:32,669 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:46:32,670 - 关键帧分析器 - INFO - 已更新scene_id 7 的observation
2025-08-17 21:46:32,670 - 关键帧分析器 - INFO - 已更新scene_id 8 的observation
2025-08-17 21:46:32,670 - 关键帧分析器 - INFO - 成功更新 2 个场景的observation
2025-08-17 21:46:32,670 - 关键帧分析器 - INFO - 第 4 批处理成功
2025-08-17 21:46:35,576 - 关键帧分析器 - INFO - API响应验证通过，包含2个有效的observation
2025-08-17 21:46:35,576 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:46:35,577 - 关键帧分析器 - INFO - 已更新scene_id 9 的observation
2025-08-17 21:46:35,577 - 关键帧分析器 - INFO - 已更新scene_id 10 的observation
2025-08-17 21:46:35,577 - 关键帧分析器 - INFO - 成功更新 2 个场景的observation
2025-08-17 21:46:35,577 - 关键帧分析器 - INFO - 第 5 批处理成功
2025-08-17 21:46:35,578 - 关键帧分析器 - INFO - 
并行处理完成: 成功 5/5 批
2025-08-17 21:46:35,718 - 关键帧分析器 - INFO - 成功保存scenes_report.json: F:\github\aicut_auto\ai-video-splitter\scenes_report.json
2025-08-17 21:46:35,718 - 关键帧分析器 - INFO - scenes_report.json已成功更新
2025-08-17 21:46:35,718 - 关键帧分析器 - INFO - 
============================================================
2025-08-17 21:46:35,719 - 关键帧分析器 - INFO - 处理完成报告
2025-08-17 21:46:35,719 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:46:35,719 - 关键帧分析器 - INFO - 总图片数量: 10
2025-08-17 21:46:35,719 - 关键帧分析器 - INFO - 成功处理批次: 5
2025-08-17 21:46:35,719 - 关键帧分析器 - INFO - 所有图片处理成功！
2025-08-17 21:46:35,719 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:51:18,097 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:51:18,097 - 关键帧分析器 - INFO - 视频关键帧分析工具启动
2025-08-17 21:51:18,097 - 关键帧分析器 - INFO - 每批处理图片数量: 2
2025-08-17 21:51:18,097 - 关键帧分析器 - INFO - 最大重试次数: 3
2025-08-17 21:51:18,097 - 关键帧分析器 - INFO - 并行线程数: 5
2025-08-17 21:51:18,097 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:51:18,097 - 关键帧分析器 - INFO - 检查scenes_report.json文件...
2025-08-17 21:51:18,137 - 关键帧分析器 - INFO - 成功加载scenes_report.json，包含 3221 个场景
2025-08-17 21:51:18,137 - 关键帧分析器 - INFO - 开始扫描目录: F:\github\aicut_auto\ai-video-splitter\Keyframes
2025-08-17 21:51:18,143 - 关键帧分析器 - INFO - 找到 3221 个关键帧图片文件
2025-08-17 21:51:18,143 - 关键帧分析器 - INFO - 开始按scene编号排序图片文件
2025-08-17 21:51:18,150 - 关键帧分析器 - INFO - 成功排序 3221 个图片文件
2025-08-17 21:51:18,150 - 关键帧分析器 - INFO - Scene编号范围: 1 - 3221
2025-08-17 21:51:18,150 - 关键帧分析器 - INFO - 检查scenes_report.json中已有的observation...
2025-08-17 21:51:18,154 - 关键帧分析器 - INFO - 总共 3221 个scene
2025-08-17 21:51:18,154 - 关键帧分析器 - INFO - 已有observation的scene: 10 个
2025-08-17 21:51:18,154 - 关键帧分析器 - INFO - 需要处理的scene: 3211 个
2025-08-17 21:51:18,154 - 关键帧分析器 - INFO - 跳过的scene_id: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
2025-08-17 21:51:18,154 - 关键帧分析器 - INFO - 限制处理图片数量为: 15
2025-08-17 21:51:18,154 - 关键帧分析器 - INFO - 准备处理 15 张图片
2025-08-17 21:51:18,154 - 关键帧分析器 - INFO - 开始并行分批处理 15 张图片，共 8 批，每批 2 张
2025-08-17 21:51:18,154 - 关键帧分析器 - INFO - 使用 5 个并行线程
2025-08-17 21:51:18,155 - 关键帧分析器 - INFO - 线程处理第 1 批: scene [11, 12]
2025-08-17 21:51:18,155 - 关键帧分析器 - INFO - 线程处理第 2 批: scene [13, 14]
2025-08-17 21:51:18,155 - 关键帧分析器 - INFO - 线程处理第 3 批: scene [15, 16]
2025-08-17 21:51:18,155 - 关键帧分析器 - INFO - 线程处理第 4 批: scene [17, 18]
2025-08-17 21:51:18,156 - 关键帧分析器 - INFO - 线程处理第 5 批: scene [19, 20]
2025-08-17 21:51:18,166 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [11, 12]
2025-08-17 21:51:18,167 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [13, 14]
2025-08-17 21:51:18,174 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [15, 16]
2025-08-17 21:51:18,175 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [19, 20]
2025-08-17 21:51:18,175 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [17, 18]
2025-08-17 21:51:20,522 - 关键帧分析器 - ERROR - 第1次尝试: 处理API响应时出错: list index out of range
2025-08-17 21:51:20,522 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [15, 16]
2025-08-17 21:51:22,188 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 21:51:22,188 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [13, 14]
2025-08-17 21:51:22,962 - 关键帧分析器 - ERROR - 第2次尝试: 处理API响应时出错: list index out of range
2025-08-17 21:51:22,962 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [15, 16]
2025-08-17 21:51:25,320 - 关键帧分析器 - ERROR - 第3次尝试: 处理API响应时出错: list index out of range
2025-08-17 21:51:25,320 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [15, 16] 分析失败
2025-08-17 21:51:25,320 - 关键帧分析器 - ERROR - 第 3 批API分析失败
2025-08-17 21:51:25,320 - 关键帧分析器 - INFO - 线程处理第 6 批: scene [21, 22]
2025-08-17 21:51:25,325 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [21, 22]
2025-08-17 21:51:26,541 - 关键帧分析器 - INFO - API响应验证通过，包含2个有效的observation
2025-08-17 21:51:26,541 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:51:26,541 - 关键帧分析器 - INFO - 已更新scene_id 11 的observation
2025-08-17 21:51:26,542 - 关键帧分析器 - INFO - 已更新scene_id 12 的observation
2025-08-17 21:51:26,542 - 关键帧分析器 - INFO - 成功更新 2 个场景的observation
2025-08-17 21:51:26,542 - 关键帧分析器 - INFO - 第 1 批处理成功
2025-08-17 21:51:26,542 - 关键帧分析器 - INFO - 线程处理第 7 批: scene [23, 24]
2025-08-17 21:51:26,545 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [23, 24]
2025-08-17 21:51:27,765 - 关键帧分析器 - INFO - API响应验证通过，包含2个有效的observation
2025-08-17 21:51:27,765 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:51:27,766 - 关键帧分析器 - INFO - 已更新scene_id 17 的observation
2025-08-17 21:51:27,766 - 关键帧分析器 - INFO - 已更新scene_id 18 的observation
2025-08-17 21:51:27,766 - 关键帧分析器 - INFO - 成功更新 2 个场景的observation
2025-08-17 21:51:27,766 - 关键帧分析器 - INFO - 第 4 批处理成功
2025-08-17 21:51:27,767 - 关键帧分析器 - INFO - 线程处理第 8 批: scene [25]
2025-08-17 21:51:27,779 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [25]
2025-08-17 21:51:27,804 - 关键帧分析器 - INFO - API响应验证通过，包含2个有效的observation
2025-08-17 21:51:27,804 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:51:27,804 - 关键帧分析器 - INFO - 已更新scene_id 19 的observation
2025-08-17 21:51:27,804 - 关键帧分析器 - INFO - 已更新scene_id 20 的observation
2025-08-17 21:51:27,805 - 关键帧分析器 - INFO - 成功更新 2 个场景的observation
2025-08-17 21:51:27,805 - 关键帧分析器 - INFO - 第 5 批处理成功
2025-08-17 21:51:30,222 - 关键帧分析器 - INFO - API响应验证通过，包含2个有效的observation
2025-08-17 21:51:30,222 - 关键帧分析器 - INFO - 第2次尝试成功，获得有效的分析结果
2025-08-17 21:51:30,222 - 关键帧分析器 - INFO - 已更新scene_id 13 的observation
2025-08-17 21:51:30,222 - 关键帧分析器 - INFO - 已更新scene_id 14 的observation
2025-08-17 21:51:30,222 - 关键帧分析器 - INFO - 成功更新 2 个场景的observation
2025-08-17 21:51:30,223 - 关键帧分析器 - INFO - 第 2 批处理成功
2025-08-17 21:51:36,619 - 关键帧分析器 - INFO - API响应验证通过，包含1个有效的observation
2025-08-17 21:51:36,619 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:51:36,619 - 关键帧分析器 - INFO - 已更新scene_id 25 的observation
2025-08-17 21:51:36,619 - 关键帧分析器 - INFO - 成功更新 1 个场景的observation
2025-08-17 21:51:36,619 - 关键帧分析器 - INFO - 第 8 批处理成功
2025-08-17 21:51:38,320 - 关键帧分析器 - INFO - API响应验证通过，包含2个有效的observation
2025-08-17 21:51:38,320 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:51:38,320 - 关键帧分析器 - INFO - 已更新scene_id 21 的observation
2025-08-17 21:51:38,321 - 关键帧分析器 - INFO - 已更新scene_id 22 的observation
2025-08-17 21:51:38,321 - 关键帧分析器 - INFO - 成功更新 2 个场景的observation
2025-08-17 21:51:38,321 - 关键帧分析器 - INFO - 第 6 批处理成功
2025-08-17 21:51:38,713 - 关键帧分析器 - INFO - API响应验证通过，包含2个有效的observation
2025-08-17 21:51:38,713 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:51:38,713 - 关键帧分析器 - INFO - 已更新scene_id 23 的observation
2025-08-17 21:51:38,713 - 关键帧分析器 - INFO - 已更新scene_id 24 的observation
2025-08-17 21:51:38,713 - 关键帧分析器 - INFO - 成功更新 2 个场景的observation
2025-08-17 21:51:38,715 - 关键帧分析器 - INFO - 第 7 批处理成功
2025-08-17 21:51:38,715 - 关键帧分析器 - INFO - 
并行处理完成: 成功 7/8 批
2025-08-17 21:51:38,715 - 关键帧分析器 - ERROR - 失败的scene_id: [15, 16]
2025-08-17 21:51:38,868 - 关键帧分析器 - INFO - 成功保存scenes_report.json: F:\github\aicut_auto\ai-video-splitter\scenes_report.json
2025-08-17 21:51:38,868 - 关键帧分析器 - INFO - scenes_report.json已成功更新
2025-08-17 21:51:38,868 - 关键帧分析器 - INFO - 
============================================================
2025-08-17 21:51:38,868 - 关键帧分析器 - INFO - 处理完成报告
2025-08-17 21:51:38,868 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:51:38,868 - 关键帧分析器 - INFO - 总图片数量: 3221
2025-08-17 21:51:38,869 - 关键帧分析器 - INFO - 需要处理的图片数量: 15
2025-08-17 21:51:38,869 - 关键帧分析器 - INFO - 成功处理批次: 7
2025-08-17 21:51:38,869 - 关键帧分析器 - ERROR - 失败的scene_id数量: 2
2025-08-17 21:51:38,869 - 关键帧分析器 - ERROR - 失败的scene_id列表: [15, 16]
2025-08-17 21:51:38,869 - 关键帧分析器 - ERROR - 建议检查:
2025-08-17 21:51:38,869 - 关键帧分析器 - ERROR - 1. 网络连接是否正常
2025-08-17 21:51:38,869 - 关键帧分析器 - ERROR - 2. API服务是否可用
2025-08-17 21:51:38,869 - 关键帧分析器 - ERROR - 3. 图片文件是否损坏
2025-08-17 21:51:38,870 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:55:46,365 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:55:46,366 - 关键帧分析器 - INFO - 视频关键帧分析工具启动
2025-08-17 21:55:46,366 - 关键帧分析器 - INFO - 每批处理图片数量: 2
2025-08-17 21:55:46,366 - 关键帧分析器 - INFO - 最大重试次数: 3
2025-08-17 21:55:46,366 - 关键帧分析器 - INFO - 并行线程数: 5
2025-08-17 21:55:46,366 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:55:46,366 - 关键帧分析器 - INFO - 检查scenes_report.json文件...
2025-08-17 21:55:46,406 - 关键帧分析器 - INFO - 成功加载scenes_report.json，包含 3221 个场景
2025-08-17 21:55:46,406 - 关键帧分析器 - INFO - 开始扫描目录: F:\github\aicut_auto\ai-video-splitter\Keyframes
2025-08-17 21:55:46,411 - 关键帧分析器 - INFO - 找到 3221 个关键帧图片文件
2025-08-17 21:55:46,411 - 关键帧分析器 - INFO - 开始按scene编号排序图片文件
2025-08-17 21:55:46,417 - 关键帧分析器 - INFO - 成功排序 3221 个图片文件
2025-08-17 21:55:46,417 - 关键帧分析器 - INFO - Scene编号范围: 1 - 3221
2025-08-17 21:55:46,417 - 关键帧分析器 - INFO - 检查scenes_report.json中已有的observation...
2025-08-17 21:55:46,423 - 关键帧分析器 - INFO - 总共 3221 个scene
2025-08-17 21:55:46,423 - 关键帧分析器 - INFO - 已有observation的scene: 23 个
2025-08-17 21:55:46,423 - 关键帧分析器 - INFO - 需要处理的scene: 3198 个
2025-08-17 21:55:46,423 - 关键帧分析器 - INFO - 跳过的scene_id: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]...
2025-08-17 21:55:46,423 - 关键帧分析器 - INFO - 限制处理图片数量为: 5
2025-08-17 21:55:46,423 - 关键帧分析器 - INFO - 准备处理 5 张图片
2025-08-17 21:55:46,423 - 关键帧分析器 - INFO - 开始并行分批处理 5 张图片，共 3 批，每批 2 张
2025-08-17 21:55:46,423 - 关键帧分析器 - INFO - 使用 5 个并行线程
2025-08-17 21:55:46,424 - 关键帧分析器 - INFO - 线程处理第 1 批: scene [15, 16]
2025-08-17 21:55:46,424 - 关键帧分析器 - INFO - 线程处理第 2 批: scene [26, 27]
2025-08-17 21:55:46,424 - 关键帧分析器 - INFO - 线程处理第 3 批: scene [28]
2025-08-17 21:55:46,429 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [28]
2025-08-17 21:55:46,433 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [26, 27]
2025-08-17 21:55:46,433 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [15, 16]
2025-08-17 21:55:49,026 - 关键帧分析器 - ERROR - 第1次尝试: API返回的choices数组为空
2025-08-17 21:55:49,026 - 关键帧分析器 - ERROR - 完整API响应: {'id': 'chatcmpl-b5fe6d3b-cec0-4a47-aedb-c3678f5a9a21', 'object': 'chat.completion', 'created': 1755438949, 'model': 'gemini-2.5-flash-preview-05-20', 'choices': [], 'usage': {'prompt_tokens': 764, 'completion_tokens': 0, 'total_tokens': 764}}
2025-08-17 21:55:49,026 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [15, 16]
2025-08-17 21:55:51,383 - 关键帧分析器 - ERROR - 第2次尝试: API返回的choices数组为空
2025-08-17 21:55:51,383 - 关键帧分析器 - ERROR - 完整API响应: {'id': 'chatcmpl-1983616b-4e3c-4da0-89e7-f3ded2831d4d', 'object': 'chat.completion', 'created': 1755438951, 'model': 'gemini-2.5-flash-preview-05-20', 'choices': [], 'usage': {'prompt_tokens': 764, 'completion_tokens': 0, 'total_tokens': 764}}
2025-08-17 21:55:51,383 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [15, 16]
2025-08-17 21:55:53,015 - 关键帧分析器 - INFO - API响应验证通过，包含1个有效的observation
2025-08-17 21:55:53,015 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:55:53,015 - 关键帧分析器 - INFO - 已更新scene_id 28 的observation
2025-08-17 21:55:53,015 - 关键帧分析器 - INFO - 成功更新 1 个场景的observation
2025-08-17 21:55:53,015 - 关键帧分析器 - INFO - 第 3 批处理成功
2025-08-17 21:55:55,186 - 关键帧分析器 - ERROR - 第3次尝试: API返回的choices数组为空
2025-08-17 21:55:55,186 - 关键帧分析器 - ERROR - 完整API响应: {'id': 'chatcmpl-70fa62c8-dc17-449f-af41-8bc98a44d3ca', 'object': 'chat.completion', 'created': 1755438955, 'model': 'gemini-2.5-flash-preview-05-20', 'choices': [], 'usage': {'prompt_tokens': 764, 'completion_tokens': 0, 'total_tokens': 764}}
2025-08-17 21:55:55,187 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [15, 16] 分析失败
2025-08-17 21:55:55,187 - 关键帧分析器 - ERROR - 第 1 批API分析失败
2025-08-17 21:55:56,239 - 关键帧分析器 - INFO - API响应验证通过，包含2个有效的observation
2025-08-17 21:55:56,239 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:55:56,239 - 关键帧分析器 - INFO - 已更新scene_id 26 的observation
2025-08-17 21:55:56,239 - 关键帧分析器 - INFO - 已更新scene_id 27 的observation
2025-08-17 21:55:56,240 - 关键帧分析器 - INFO - 成功更新 2 个场景的observation
2025-08-17 21:55:56,240 - 关键帧分析器 - INFO - 第 2 批处理成功
2025-08-17 21:55:56,240 - 关键帧分析器 - INFO - 
并行处理完成: 成功 2/3 批
2025-08-17 21:55:56,240 - 关键帧分析器 - ERROR - 失败的scene_id: [15, 16]
2025-08-17 21:55:56,385 - 关键帧分析器 - INFO - 成功保存scenes_report.json: F:\github\aicut_auto\ai-video-splitter\scenes_report.json
2025-08-17 21:55:56,385 - 关键帧分析器 - INFO - scenes_report.json已成功更新
2025-08-17 21:55:56,385 - 关键帧分析器 - INFO - 
============================================================
2025-08-17 21:55:56,385 - 关键帧分析器 - INFO - 处理完成报告
2025-08-17 21:55:56,385 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:55:56,385 - 关键帧分析器 - INFO - 总图片数量: 3221
2025-08-17 21:55:56,385 - 关键帧分析器 - INFO - 需要处理的图片数量: 5
2025-08-17 21:55:56,386 - 关键帧分析器 - INFO - 成功处理批次: 2
2025-08-17 21:55:56,386 - 关键帧分析器 - ERROR - 失败的scene_id数量: 2
2025-08-17 21:55:56,386 - 关键帧分析器 - ERROR - 失败的scene_id列表: [15, 16]
2025-08-17 21:55:56,386 - 关键帧分析器 - ERROR - 建议检查:
2025-08-17 21:55:56,386 - 关键帧分析器 - ERROR - 1. 网络连接是否正常
2025-08-17 21:55:56,386 - 关键帧分析器 - ERROR - 2. API服务是否可用
2025-08-17 21:55:56,386 - 关键帧分析器 - ERROR - 3. 图片文件是否损坏
2025-08-17 21:55:56,386 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:57:22,536 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:57:22,537 - 关键帧分析器 - INFO - 视频关键帧分析工具启动
2025-08-17 21:57:22,537 - 关键帧分析器 - INFO - 每批处理图片数量: 10
2025-08-17 21:57:22,537 - 关键帧分析器 - INFO - 最大重试次数: 3
2025-08-17 21:57:22,537 - 关键帧分析器 - INFO - 并行线程数: 5
2025-08-17 21:57:22,537 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:57:22,537 - 关键帧分析器 - INFO - 检查scenes_report.json文件...
2025-08-17 21:57:22,575 - 关键帧分析器 - INFO - 成功加载scenes_report.json，包含 3221 个场景
2025-08-17 21:57:22,575 - 关键帧分析器 - INFO - 开始扫描目录: F:\github\aicut_auto\ai-video-splitter\Keyframes
2025-08-17 21:57:22,581 - 关键帧分析器 - INFO - 找到 3221 个关键帧图片文件
2025-08-17 21:57:22,582 - 关键帧分析器 - INFO - 开始按scene编号排序图片文件
2025-08-17 21:57:22,588 - 关键帧分析器 - INFO - 成功排序 3221 个图片文件
2025-08-17 21:57:22,588 - 关键帧分析器 - INFO - Scene编号范围: 1 - 3221
2025-08-17 21:57:22,588 - 关键帧分析器 - INFO - 检查scenes_report.json中已有的observation...
2025-08-17 21:57:22,589 - 关键帧分析器 - INFO - 总共 3221 个scene
2025-08-17 21:57:22,589 - 关键帧分析器 - INFO - 已有observation的scene: 26 个
2025-08-17 21:57:22,589 - 关键帧分析器 - INFO - 需要处理的scene: 3195 个
2025-08-17 21:57:22,589 - 关键帧分析器 - INFO - 跳过的scene_id: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]...
2025-08-17 21:57:22,590 - 关键帧分析器 - INFO - 准备处理 3195 张图片
2025-08-17 21:57:22,590 - 关键帧分析器 - INFO - 开始并行分批处理 3195 张图片，共 320 批，每批 10 张
2025-08-17 21:57:22,590 - 关键帧分析器 - INFO - 使用 5 个并行线程
2025-08-17 21:57:22,593 - 关键帧分析器 - INFO - 线程处理第 1 批: scene [15, 16, 29, 30, 31, 32, 33, 34, 35, 36]
2025-08-17 21:57:22,593 - 关键帧分析器 - INFO - 线程处理第 2 批: scene [37, 38, 39, 40, 41, 42, 43, 44, 45, 46]
2025-08-17 21:57:22,593 - 关键帧分析器 - INFO - 线程处理第 3 批: scene [47, 48, 49, 50, 51, 52, 53, 54, 55, 56]
2025-08-17 21:57:22,594 - 关键帧分析器 - INFO - 线程处理第 4 批: scene [57, 58, 59, 60, 61, 62, 63, 64, 65, 66]
2025-08-17 21:57:22,595 - 关键帧分析器 - INFO - 线程处理第 5 批: scene [67, 68, 69, 70, 71, 72, 73, 74, 75, 76]
2025-08-17 21:57:22,651 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [15, 16, 29, 30, 31, 32, 33, 34, 35, 36]
2025-08-17 21:57:22,693 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [57, 58, 59, 60, 61, 62, 63, 64, 65, 66]
2025-08-17 21:57:22,699 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [67, 68, 69, 70, 71, 72, 73, 74, 75, 76]
2025-08-17 21:57:22,701 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [47, 48, 49, 50, 51, 52, 53, 54, 55, 56]
2025-08-17 21:57:22,749 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [37, 38, 39, 40, 41, 42, 43, 44, 45, 46]
2025-08-17 21:57:29,160 - 关键帧分析器 - ERROR - 第1次尝试: API返回的choices数组为空
2025-08-17 21:57:29,160 - 关键帧分析器 - ERROR - 完整API响应: {'id': 'chatcmpl-2f09a566-724c-4b1b-9a28-1dc0b472dc0a', 'object': 'chat.completion', 'created': 1755439049, 'model': 'gemini-2.5-flash-preview-05-20', 'choices': [], 'usage': {'prompt_tokens': 2831, 'completion_tokens': 0, 'total_tokens': 2831}}
2025-08-17 21:57:29,160 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [47, 48, 49, 50, 51, 52, 53, 54, 55, 56]
2025-08-17 21:57:32,725 - 关键帧分析器 - ERROR - 第1次尝试: API返回的choices数组为空
2025-08-17 21:57:32,725 - 关键帧分析器 - ERROR - 完整API响应: {'id': 'chatcmpl-d35dcf9c-d938-4f63-9c4d-c05d6b53142a', 'object': 'chat.completion', 'created': 1755439052, 'model': 'gemini-2.5-flash-preview-05-20', 'choices': [], 'usage': {'prompt_tokens': 2831, 'completion_tokens': 0, 'total_tokens': 2831}}
2025-08-17 21:57:32,726 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [15, 16, 29, 30, 31, 32, 33, 34, 35, 36]
2025-08-17 21:57:34,442 - 关键帧分析器 - ERROR - 第2次尝试: API返回的choices数组为空
2025-08-17 21:57:34,443 - 关键帧分析器 - ERROR - 完整API响应: {'id': 'chatcmpl-13e79ff0-a255-4e8d-b54d-4836cac6441d', 'object': 'chat.completion', 'created': 1755439054, 'model': 'gemini-2.5-flash-preview-05-20', 'choices': [], 'usage': {'prompt_tokens': 2831, 'completion_tokens': 0, 'total_tokens': 2831}}
2025-08-17 21:57:34,443 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [47, 48, 49, 50, 51, 52, 53, 54, 55, 56]
2025-08-17 21:57:35,933 - 关键帧分析器 - ERROR - 第2次尝试: API返回的choices数组为空
2025-08-17 21:57:35,933 - 关键帧分析器 - ERROR - 完整API响应: {'id': 'chatcmpl-635db00c-8ddc-4fe2-bb79-f305f833441a', 'object': 'chat.completion', 'created': 1755439055, 'model': 'gemini-2.5-flash-preview-05-20', 'choices': [], 'usage': {'prompt_tokens': 2831, 'completion_tokens': 0, 'total_tokens': 2831}}
2025-08-17 21:57:35,934 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [15, 16, 29, 30, 31, 32, 33, 34, 35, 36]
2025-08-17 21:57:37,687 - 关键帧分析器 - ERROR - 第3次尝试: API返回的choices数组为空
2025-08-17 21:57:37,687 - 关键帧分析器 - ERROR - 完整API响应: {'id': 'chatcmpl-cfcea48e-c26d-47a6-8cbb-a00112f41c74', 'object': 'chat.completion', 'created': 1755439057, 'model': 'gemini-2.5-flash-preview-05-20', 'choices': [], 'usage': {'prompt_tokens': 2831, 'completion_tokens': 0, 'total_tokens': 2831}}
2025-08-17 21:57:37,687 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [47, 48, 49, 50, 51, 52, 53, 54, 55, 56] 分析失败
2025-08-17 21:57:37,687 - 关键帧分析器 - ERROR - 第 3 批API分析失败
2025-08-17 21:57:37,688 - 关键帧分析器 - INFO - 线程处理第 6 批: scene [77, 78, 79, 80, 81, 82, 83, 84, 85, 86]
2025-08-17 21:57:37,721 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [77, 78, 79, 80, 81, 82, 83, 84, 85, 86]
2025-08-17 21:57:40,518 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 21:57:40,518 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:57:40,518 - 关键帧分析器 - INFO - 已更新scene_id 57 的observation
2025-08-17 21:57:40,518 - 关键帧分析器 - INFO - 已更新scene_id 58 的observation
2025-08-17 21:57:40,518 - 关键帧分析器 - INFO - 已更新scene_id 59 的observation
2025-08-17 21:57:40,518 - 关键帧分析器 - INFO - 已更新scene_id 60 的observation
2025-08-17 21:57:40,519 - 关键帧分析器 - INFO - 已更新scene_id 61 的observation
2025-08-17 21:57:40,519 - 关键帧分析器 - INFO - 已更新scene_id 62 的observation
2025-08-17 21:57:40,519 - 关键帧分析器 - INFO - 已更新scene_id 63 的observation
2025-08-17 21:57:40,519 - 关键帧分析器 - INFO - 已更新scene_id 64 的observation
2025-08-17 21:57:40,519 - 关键帧分析器 - INFO - 已更新scene_id 65 的observation
2025-08-17 21:57:40,519 - 关键帧分析器 - INFO - 已更新scene_id 66 的observation
2025-08-17 21:57:40,519 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 21:57:40,519 - 关键帧分析器 - INFO - 第 4 批处理成功
2025-08-17 21:57:40,519 - 关键帧分析器 - INFO - 线程处理第 7 批: scene [87, 88, 89, 90, 91, 92, 93, 94, 95, 96]
2025-08-17 21:57:40,551 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [87, 88, 89, 90, 91, 92, 93, 94, 95, 96]
2025-08-17 21:57:42,049 - 关键帧分析器 - ERROR - 第3次尝试: API返回的choices数组为空
2025-08-17 21:57:42,049 - 关键帧分析器 - ERROR - 完整API响应: {'id': 'chatcmpl-51fcc729-4f0c-41aa-a623-35d94a1ee9ff', 'object': 'chat.completion', 'created': 1755439062, 'model': 'gemini-2.5-flash-preview-05-20', 'choices': [], 'usage': {'prompt_tokens': 2831, 'completion_tokens': 0, 'total_tokens': 2831}}
2025-08-17 21:57:42,049 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [15, 16, 29, 30, 31, 32, 33, 34, 35, 36] 分析失败
2025-08-17 21:57:42,049 - 关键帧分析器 - ERROR - 第 1 批API分析失败
2025-08-17 21:57:42,049 - 关键帧分析器 - INFO - 线程处理第 8 批: scene [97, 98, 99, 100, 101, 102, 103, 104, 105, 106]
2025-08-17 21:57:42,073 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [97, 98, 99, 100, 101, 102, 103, 104, 105, 106]
2025-08-17 21:57:45,023 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 21:57:45,024 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:57:45,024 - 关键帧分析器 - INFO - 已更新scene_id 37 的observation
2025-08-17 21:57:45,024 - 关键帧分析器 - INFO - 已更新scene_id 38 的observation
2025-08-17 21:57:45,024 - 关键帧分析器 - INFO - 已更新scene_id 39 的observation
2025-08-17 21:57:45,024 - 关键帧分析器 - INFO - 已更新scene_id 40 的observation
2025-08-17 21:57:45,024 - 关键帧分析器 - INFO - 已更新scene_id 41 的observation
2025-08-17 21:57:45,024 - 关键帧分析器 - INFO - 已更新scene_id 42 的observation
2025-08-17 21:57:45,024 - 关键帧分析器 - INFO - 已更新scene_id 43 的observation
2025-08-17 21:57:45,024 - 关键帧分析器 - INFO - 已更新scene_id 44 的observation
2025-08-17 21:57:45,024 - 关键帧分析器 - INFO - 已更新scene_id 45 的observation
2025-08-17 21:57:45,024 - 关键帧分析器 - INFO - 已更新scene_id 46 的observation
2025-08-17 21:57:45,024 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 21:57:45,024 - 关键帧分析器 - INFO - 第 2 批处理成功
2025-08-17 21:57:45,024 - 关键帧分析器 - INFO - 线程处理第 9 批: scene [107, 108, 109, 110, 111, 112, 113, 114, 115, 116]
2025-08-17 21:57:45,054 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [107, 108, 109, 110, 111, 112, 113, 114, 115, 116]
2025-08-17 21:57:46,840 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 21:57:46,841 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:57:46,841 - 关键帧分析器 - INFO - 已更新scene_id 67 的observation
2025-08-17 21:57:46,841 - 关键帧分析器 - INFO - 已更新scene_id 68 的observation
2025-08-17 21:57:46,841 - 关键帧分析器 - INFO - 已更新scene_id 69 的observation
2025-08-17 21:57:46,841 - 关键帧分析器 - INFO - 已更新scene_id 70 的observation
2025-08-17 21:57:46,841 - 关键帧分析器 - INFO - 已更新scene_id 71 的observation
2025-08-17 21:57:46,841 - 关键帧分析器 - INFO - 已更新scene_id 72 的observation
2025-08-17 21:57:46,841 - 关键帧分析器 - INFO - 已更新scene_id 73 的observation
2025-08-17 21:57:46,841 - 关键帧分析器 - INFO - 已更新scene_id 74 的observation
2025-08-17 21:57:46,842 - 关键帧分析器 - INFO - 已更新scene_id 75 的observation
2025-08-17 21:57:46,842 - 关键帧分析器 - INFO - 已更新scene_id 76 的observation
2025-08-17 21:57:46,842 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 21:57:46,842 - 关键帧分析器 - INFO - 第 5 批处理成功
2025-08-17 21:57:46,842 - 关键帧分析器 - INFO - 线程处理第 10 批: scene [117, 118, 119, 120, 121, 122, 123, 124, 125, 126]
2025-08-17 21:57:46,871 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [117, 118, 119, 120, 121, 122, 123, 124, 125, 126]
2025-08-17 21:57:56,112 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 21:57:56,112 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:57:56,112 - 关键帧分析器 - INFO - 已更新scene_id 77 的observation
2025-08-17 21:57:56,112 - 关键帧分析器 - INFO - 已更新scene_id 78 的observation
2025-08-17 21:57:56,112 - 关键帧分析器 - INFO - 已更新scene_id 79 的observation
2025-08-17 21:57:56,112 - 关键帧分析器 - INFO - 已更新scene_id 80 的observation
2025-08-17 21:57:56,113 - 关键帧分析器 - INFO - 已更新scene_id 81 的observation
2025-08-17 21:57:56,113 - 关键帧分析器 - INFO - 已更新scene_id 82 的observation
2025-08-17 21:57:56,113 - 关键帧分析器 - INFO - 已更新scene_id 83 的observation
2025-08-17 21:57:56,113 - 关键帧分析器 - INFO - 已更新scene_id 84 的observation
2025-08-17 21:57:56,113 - 关键帧分析器 - INFO - 已更新scene_id 85 的observation
2025-08-17 21:57:56,113 - 关键帧分析器 - INFO - 已更新scene_id 86 的observation
2025-08-17 21:57:56,113 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 21:57:56,113 - 关键帧分析器 - INFO - 第 6 批处理成功
2025-08-17 21:57:56,114 - 关键帧分析器 - INFO - 线程处理第 11 批: scene [127, 128, 129, 130, 131, 132, 133, 134, 135, 136]
2025-08-17 21:57:56,143 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [127, 128, 129, 130, 131, 132, 133, 134, 135, 136]
2025-08-17 21:57:57,944 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 21:57:57,944 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:57:57,944 - 关键帧分析器 - INFO - 已更新scene_id 87 的observation
2025-08-17 21:57:57,944 - 关键帧分析器 - INFO - 已更新scene_id 88 的observation
2025-08-17 21:57:57,944 - 关键帧分析器 - INFO - 已更新scene_id 89 的observation
2025-08-17 21:57:57,944 - 关键帧分析器 - INFO - 已更新scene_id 90 的observation
2025-08-17 21:57:57,944 - 关键帧分析器 - INFO - 已更新scene_id 91 的observation
2025-08-17 21:57:57,945 - 关键帧分析器 - INFO - 已更新scene_id 92 的observation
2025-08-17 21:57:57,945 - 关键帧分析器 - INFO - 已更新scene_id 93 的observation
2025-08-17 21:57:57,945 - 关键帧分析器 - INFO - 已更新scene_id 94 的observation
2025-08-17 21:57:57,945 - 关键帧分析器 - INFO - 已更新scene_id 95 的observation
2025-08-17 21:57:57,945 - 关键帧分析器 - INFO - 已更新scene_id 96 的observation
2025-08-17 21:57:57,945 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 21:57:57,945 - 关键帧分析器 - INFO - 第 7 批处理成功
2025-08-17 21:57:57,945 - 关键帧分析器 - INFO - 线程处理第 12 批: scene [137, 138, 139, 140, 141, 142, 143, 144, 145, 146]
2025-08-17 21:57:57,963 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [137, 138, 139, 140, 141, 142, 143, 144, 145, 146]
2025-08-17 21:57:58,590 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 21:57:58,590 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:57:58,590 - 关键帧分析器 - INFO - 已更新scene_id 97 的observation
2025-08-17 21:57:58,590 - 关键帧分析器 - INFO - 已更新scene_id 98 的observation
2025-08-17 21:57:58,590 - 关键帧分析器 - INFO - 已更新scene_id 99 的observation
2025-08-17 21:57:58,591 - 关键帧分析器 - INFO - 已更新scene_id 100 的observation
2025-08-17 21:57:58,591 - 关键帧分析器 - INFO - 已更新scene_id 101 的observation
2025-08-17 21:57:58,591 - 关键帧分析器 - INFO - 已更新scene_id 102 的observation
2025-08-17 21:57:58,591 - 关键帧分析器 - INFO - 已更新scene_id 103 的observation
2025-08-17 21:57:58,591 - 关键帧分析器 - INFO - 已更新scene_id 104 的observation
2025-08-17 21:57:58,591 - 关键帧分析器 - INFO - 已更新scene_id 105 的observation
2025-08-17 21:57:58,591 - 关键帧分析器 - INFO - 已更新scene_id 106 的observation
2025-08-17 21:57:58,591 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 21:57:58,591 - 关键帧分析器 - INFO - 第 8 批处理成功
2025-08-17 21:57:58,591 - 关键帧分析器 - INFO - 线程处理第 13 批: scene [147, 148, 149, 150, 151, 152, 153, 154, 155, 156]
2025-08-17 21:57:58,631 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [147, 148, 149, 150, 151, 152, 153, 154, 155, 156]
2025-08-17 21:57:59,122 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 21:57:59,124 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:57:59,124 - 关键帧分析器 - INFO - 已更新scene_id 107 的observation
2025-08-17 21:57:59,126 - 关键帧分析器 - INFO - 已更新scene_id 108 的observation
2025-08-17 21:57:59,126 - 关键帧分析器 - INFO - 已更新scene_id 109 的observation
2025-08-17 21:57:59,126 - 关键帧分析器 - INFO - 已更新scene_id 110 的observation
2025-08-17 21:57:59,126 - 关键帧分析器 - INFO - 已更新scene_id 111 的observation
2025-08-17 21:57:59,126 - 关键帧分析器 - INFO - 已更新scene_id 112 的observation
2025-08-17 21:57:59,126 - 关键帧分析器 - INFO - 已更新scene_id 113 的observation
2025-08-17 21:57:59,126 - 关键帧分析器 - INFO - 已更新scene_id 114 的observation
2025-08-17 21:57:59,126 - 关键帧分析器 - INFO - 已更新scene_id 115 的observation
2025-08-17 21:57:59,126 - 关键帧分析器 - INFO - 已更新scene_id 116 的observation
2025-08-17 21:57:59,126 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 21:57:59,126 - 关键帧分析器 - INFO - 第 9 批处理成功
2025-08-17 21:57:59,126 - 关键帧分析器 - INFO - 线程处理第 14 批: scene [157, 158, 159, 160, 161, 162, 163, 164, 165, 166]
2025-08-17 21:57:59,151 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [157, 158, 159, 160, 161, 162, 163, 164, 165, 166]
2025-08-17 21:58:04,373 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 21:58:04,374 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:58:04,374 - 关键帧分析器 - INFO - 已更新scene_id 117 的observation
2025-08-17 21:58:04,374 - 关键帧分析器 - INFO - 已更新scene_id 118 的observation
2025-08-17 21:58:04,374 - 关键帧分析器 - INFO - 已更新scene_id 119 的observation
2025-08-17 21:58:04,374 - 关键帧分析器 - INFO - 已更新scene_id 120 的observation
2025-08-17 21:58:04,374 - 关键帧分析器 - INFO - 已更新scene_id 121 的observation
2025-08-17 21:58:04,374 - 关键帧分析器 - INFO - 已更新scene_id 122 的observation
2025-08-17 21:58:04,374 - 关键帧分析器 - INFO - 已更新scene_id 123 的observation
2025-08-17 21:58:04,374 - 关键帧分析器 - INFO - 已更新scene_id 124 的observation
2025-08-17 21:58:04,374 - 关键帧分析器 - INFO - 已更新scene_id 125 的observation
2025-08-17 21:58:04,374 - 关键帧分析器 - INFO - 已更新scene_id 126 的observation
2025-08-17 21:58:04,374 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 21:58:04,374 - 关键帧分析器 - INFO - 第 10 批处理成功
2025-08-17 21:58:04,374 - 关键帧分析器 - INFO - 线程处理第 15 批: scene [167, 168, 169, 170, 171, 172, 173, 174, 175, 176]
2025-08-17 21:58:04,409 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [167, 168, 169, 170, 171, 172, 173, 174, 175, 176]
2025-08-17 21:58:10,703 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 21:58:10,704 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:58:10,704 - 关键帧分析器 - INFO - 已更新scene_id 127 的observation
2025-08-17 21:58:10,704 - 关键帧分析器 - INFO - 已更新scene_id 128 的observation
2025-08-17 21:58:10,704 - 关键帧分析器 - INFO - 已更新scene_id 129 的observation
2025-08-17 21:58:10,704 - 关键帧分析器 - INFO - 已更新scene_id 130 的observation
2025-08-17 21:58:10,704 - 关键帧分析器 - INFO - 已更新scene_id 131 的observation
2025-08-17 21:58:10,704 - 关键帧分析器 - INFO - 已更新scene_id 132 的observation
2025-08-17 21:58:10,704 - 关键帧分析器 - INFO - 已更新scene_id 133 的observation
2025-08-17 21:58:10,704 - 关键帧分析器 - INFO - 已更新scene_id 134 的observation
2025-08-17 21:58:10,704 - 关键帧分析器 - INFO - 已更新scene_id 135 的observation
2025-08-17 21:58:10,704 - 关键帧分析器 - INFO - 已更新scene_id 136 的observation
2025-08-17 21:58:10,704 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 21:58:10,704 - 关键帧分析器 - INFO - 第 11 批处理成功
2025-08-17 21:58:10,704 - 关键帧分析器 - INFO - 线程处理第 16 批: scene [177, 178, 179, 180, 181, 182, 183, 184, 185, 186]
2025-08-17 21:58:10,740 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [177, 178, 179, 180, 181, 182, 183, 184, 185, 186]
2025-08-17 21:58:11,956 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 21:58:11,956 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:58:11,957 - 关键帧分析器 - INFO - 已更新scene_id 137 的observation
2025-08-17 21:58:11,957 - 关键帧分析器 - INFO - 已更新scene_id 138 的observation
2025-08-17 21:58:11,957 - 关键帧分析器 - INFO - 已更新scene_id 139 的observation
2025-08-17 21:58:11,957 - 关键帧分析器 - INFO - 已更新scene_id 140 的observation
2025-08-17 21:58:11,957 - 关键帧分析器 - INFO - 已更新scene_id 141 的observation
2025-08-17 21:58:11,957 - 关键帧分析器 - INFO - 已更新scene_id 142 的observation
2025-08-17 21:58:11,957 - 关键帧分析器 - INFO - 已更新scene_id 143 的observation
2025-08-17 21:58:11,957 - 关键帧分析器 - INFO - 已更新scene_id 144 的observation
2025-08-17 21:58:11,957 - 关键帧分析器 - INFO - 已更新scene_id 145 的observation
2025-08-17 21:58:11,957 - 关键帧分析器 - INFO - 已更新scene_id 146 的observation
2025-08-17 21:58:11,958 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 21:58:11,958 - 关键帧分析器 - INFO - 第 12 批处理成功
2025-08-17 21:58:11,958 - 关键帧分析器 - INFO - 线程处理第 17 批: scene [187, 188, 189, 190, 191, 192, 193, 194, 195, 196]
2025-08-17 21:58:11,993 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [187, 188, 189, 190, 191, 192, 193, 194, 195, 196]
2025-08-17 21:58:16,393 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 21:58:16,393 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:58:16,393 - 关键帧分析器 - INFO - 已更新scene_id 147 的observation
2025-08-17 21:58:16,393 - 关键帧分析器 - INFO - 已更新scene_id 148 的observation
2025-08-17 21:58:16,393 - 关键帧分析器 - INFO - 已更新scene_id 149 的observation
2025-08-17 21:58:16,393 - 关键帧分析器 - INFO - 已更新scene_id 150 的observation
2025-08-17 21:58:16,394 - 关键帧分析器 - INFO - 已更新scene_id 151 的observation
2025-08-17 21:58:16,394 - 关键帧分析器 - INFO - 已更新scene_id 152 的observation
2025-08-17 21:58:16,394 - 关键帧分析器 - INFO - 已更新scene_id 153 的observation
2025-08-17 21:58:16,394 - 关键帧分析器 - INFO - 已更新scene_id 154 的observation
2025-08-17 21:58:16,394 - 关键帧分析器 - INFO - 已更新scene_id 155 的observation
2025-08-17 21:58:16,394 - 关键帧分析器 - INFO - 已更新scene_id 156 的observation
2025-08-17 21:58:16,394 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 21:58:16,394 - 关键帧分析器 - INFO - 第 13 批处理成功
2025-08-17 21:58:16,394 - 关键帧分析器 - INFO - 线程处理第 18 批: scene [197, 198, 199, 200, 201, 202, 203, 204, 205, 206]
2025-08-17 21:58:16,397 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 21:58:16,397 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:58:16,398 - 关键帧分析器 - INFO - 已更新scene_id 167 的observation
2025-08-17 21:58:16,398 - 关键帧分析器 - INFO - 已更新scene_id 168 的observation
2025-08-17 21:58:16,398 - 关键帧分析器 - INFO - 已更新scene_id 169 的observation
2025-08-17 21:58:16,398 - 关键帧分析器 - INFO - 已更新scene_id 170 的observation
2025-08-17 21:58:16,398 - 关键帧分析器 - INFO - 已更新scene_id 171 的observation
2025-08-17 21:58:16,398 - 关键帧分析器 - INFO - 已更新scene_id 172 的observation
2025-08-17 21:58:16,398 - 关键帧分析器 - INFO - 已更新scene_id 173 的observation
2025-08-17 21:58:16,398 - 关键帧分析器 - INFO - 已更新scene_id 174 的observation
2025-08-17 21:58:16,398 - 关键帧分析器 - INFO - 已更新scene_id 175 的observation
2025-08-17 21:58:16,398 - 关键帧分析器 - INFO - 已更新scene_id 176 的observation
2025-08-17 21:58:16,398 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 21:58:16,398 - 关键帧分析器 - INFO - 第 15 批处理成功
2025-08-17 21:58:16,399 - 关键帧分析器 - INFO - 线程处理第 19 批: scene [207, 208, 209, 210, 211, 212, 213, 214, 215, 216]
2025-08-17 21:58:16,449 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [197, 198, 199, 200, 201, 202, 203, 204, 205, 206]
2025-08-17 21:58:16,449 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [207, 208, 209, 210, 211, 212, 213, 214, 215, 216]
2025-08-17 21:58:23,902 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 21:58:23,902 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:58:23,902 - 关键帧分析器 - INFO - 已更新scene_id 157 的observation
2025-08-17 21:58:23,902 - 关键帧分析器 - INFO - 已更新scene_id 158 的observation
2025-08-17 21:58:23,902 - 关键帧分析器 - INFO - 已更新scene_id 159 的observation
2025-08-17 21:58:23,903 - 关键帧分析器 - INFO - 已更新scene_id 160 的observation
2025-08-17 21:58:23,903 - 关键帧分析器 - INFO - 已更新scene_id 161 的observation
2025-08-17 21:58:23,903 - 关键帧分析器 - INFO - 已更新scene_id 162 的observation
2025-08-17 21:58:23,903 - 关键帧分析器 - INFO - 已更新scene_id 163 的observation
2025-08-17 21:58:23,903 - 关键帧分析器 - INFO - 已更新scene_id 164 的observation
2025-08-17 21:58:23,903 - 关键帧分析器 - INFO - 已更新scene_id 165 的observation
2025-08-17 21:58:23,903 - 关键帧分析器 - INFO - 已更新scene_id 166 的observation
2025-08-17 21:58:23,903 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 21:58:23,903 - 关键帧分析器 - INFO - 第 14 批处理成功
2025-08-17 21:58:23,903 - 关键帧分析器 - INFO - 线程处理第 20 批: scene [217, 218, 219, 220, 221, 222, 223, 224, 225, 226]
2025-08-17 21:58:23,936 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [217, 218, 219, 220, 221, 222, 223, 224, 225, 226]
2025-08-17 21:58:25,870 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 21:58:25,870 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:58:25,870 - 关键帧分析器 - INFO - 已更新scene_id 177 的observation
2025-08-17 21:58:25,870 - 关键帧分析器 - INFO - 已更新scene_id 178 的observation
2025-08-17 21:58:25,870 - 关键帧分析器 - INFO - 已更新scene_id 179 的observation
2025-08-17 21:58:25,871 - 关键帧分析器 - INFO - 已更新scene_id 180 的observation
2025-08-17 21:58:25,871 - 关键帧分析器 - INFO - 已更新scene_id 181 的observation
2025-08-17 21:58:25,871 - 关键帧分析器 - INFO - 已更新scene_id 182 的observation
2025-08-17 21:58:25,871 - 关键帧分析器 - INFO - 已更新scene_id 183 的observation
2025-08-17 21:58:25,871 - 关键帧分析器 - INFO - 已更新scene_id 184 的observation
2025-08-17 21:58:25,871 - 关键帧分析器 - INFO - 已更新scene_id 185 的observation
2025-08-17 21:58:25,871 - 关键帧分析器 - INFO - 已更新scene_id 186 的observation
2025-08-17 21:58:25,871 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 21:58:25,871 - 关键帧分析器 - INFO - 第 16 批处理成功
2025-08-17 21:58:25,871 - 关键帧分析器 - INFO - 线程处理第 21 批: scene [227, 228, 229, 230, 231, 232, 233, 234, 235, 236]
2025-08-17 21:58:25,904 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [227, 228, 229, 230, 231, 232, 233, 234, 235, 236]
2025-08-17 21:58:28,943 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 21:58:28,943 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:58:28,943 - 关键帧分析器 - INFO - 已更新scene_id 187 的observation
2025-08-17 21:58:28,943 - 关键帧分析器 - INFO - 已更新scene_id 188 的observation
2025-08-17 21:58:28,945 - 关键帧分析器 - INFO - 已更新scene_id 189 的observation
2025-08-17 21:58:28,945 - 关键帧分析器 - INFO - 已更新scene_id 190 的observation
2025-08-17 21:58:28,945 - 关键帧分析器 - INFO - 已更新scene_id 191 的observation
2025-08-17 21:58:28,945 - 关键帧分析器 - INFO - 已更新scene_id 192 的observation
2025-08-17 21:58:28,945 - 关键帧分析器 - INFO - 已更新scene_id 193 的observation
2025-08-17 21:58:28,945 - 关键帧分析器 - INFO - 已更新scene_id 194 的observation
2025-08-17 21:58:28,945 - 关键帧分析器 - INFO - 已更新scene_id 195 的observation
2025-08-17 21:58:28,945 - 关键帧分析器 - INFO - 已更新scene_id 196 的observation
2025-08-17 21:58:28,945 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 21:58:28,945 - 关键帧分析器 - INFO - 第 17 批处理成功
2025-08-17 21:58:28,945 - 关键帧分析器 - INFO - 线程处理第 22 批: scene [237, 238, 239, 240, 241, 242, 243, 244, 245, 246]
2025-08-17 21:58:28,977 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [237, 238, 239, 240, 241, 242, 243, 244, 245, 246]
2025-08-17 21:58:30,963 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 21:58:30,963 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:58:30,963 - 关键帧分析器 - INFO - 已更新scene_id 197 的observation
2025-08-17 21:58:30,963 - 关键帧分析器 - INFO - 已更新scene_id 198 的observation
2025-08-17 21:58:30,964 - 关键帧分析器 - INFO - 已更新scene_id 199 的observation
2025-08-17 21:58:30,964 - 关键帧分析器 - INFO - 已更新scene_id 200 的observation
2025-08-17 21:58:30,964 - 关键帧分析器 - INFO - 已更新scene_id 201 的observation
2025-08-17 21:58:30,964 - 关键帧分析器 - INFO - 已更新scene_id 202 的observation
2025-08-17 21:58:30,964 - 关键帧分析器 - INFO - 已更新scene_id 203 的observation
2025-08-17 21:58:30,964 - 关键帧分析器 - INFO - 已更新scene_id 204 的observation
2025-08-17 21:58:30,964 - 关键帧分析器 - INFO - 已更新scene_id 205 的observation
2025-08-17 21:58:30,964 - 关键帧分析器 - INFO - 已更新scene_id 206 的observation
2025-08-17 21:58:30,964 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 21:58:30,964 - 关键帧分析器 - INFO - 第 18 批处理成功
2025-08-17 21:58:30,964 - 关键帧分析器 - INFO - 线程处理第 23 批: scene [247, 248, 249, 250, 251, 252, 253, 254, 255, 256]
2025-08-17 21:58:30,998 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [247, 248, 249, 250, 251, 252, 253, 254, 255, 256]
2025-08-17 21:58:33,505 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 21:58:33,505 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:58:33,505 - 关键帧分析器 - INFO - 已更新scene_id 207 的observation
2025-08-17 21:58:33,505 - 关键帧分析器 - INFO - 已更新scene_id 208 的observation
2025-08-17 21:58:33,505 - 关键帧分析器 - INFO - 已更新scene_id 209 的observation
2025-08-17 21:58:33,505 - 关键帧分析器 - INFO - 已更新scene_id 210 的observation
2025-08-17 21:58:33,505 - 关键帧分析器 - INFO - 已更新scene_id 211 的observation
2025-08-17 21:58:33,505 - 关键帧分析器 - INFO - 已更新scene_id 212 的observation
2025-08-17 21:58:33,505 - 关键帧分析器 - INFO - 已更新scene_id 213 的observation
2025-08-17 21:58:33,505 - 关键帧分析器 - INFO - 已更新scene_id 214 的observation
2025-08-17 21:58:33,506 - 关键帧分析器 - INFO - 已更新scene_id 215 的observation
2025-08-17 21:58:33,506 - 关键帧分析器 - INFO - 已更新scene_id 216 的observation
2025-08-17 21:58:33,506 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 21:58:33,506 - 关键帧分析器 - INFO - 第 19 批处理成功
2025-08-17 21:58:33,506 - 关键帧分析器 - INFO - 线程处理第 24 批: scene [257, 258, 259, 260, 261, 262, 263, 264, 265, 266]
2025-08-17 21:58:33,538 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [257, 258, 259, 260, 261, 262, 263, 264, 265, 266]
2025-08-17 21:58:36,462 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 21:58:36,462 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [217, 218, 219, 220, 221, 222, 223, 224, 225, 226]
2025-08-17 21:58:36,623 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 21:58:36,623 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [227, 228, 229, 230, 231, 232, 233, 234, 235, 236]
2025-08-17 21:58:46,195 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 21:58:46,195 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [237, 238, 239, 240, 241, 242, 243, 244, 245, 246]
2025-08-17 21:58:49,638 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 21:58:49,639 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [247, 248, 249, 250, 251, 252, 253, 254, 255, 256]
2025-08-17 21:58:51,008 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 21:58:51,008 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [257, 258, 259, 260, 261, 262, 263, 264, 265, 266]
2025-08-17 21:58:54,105 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 21:58:54,105 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [217, 218, 219, 220, 221, 222, 223, 224, 225, 226]
2025-08-17 21:58:58,226 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 21:58:58,226 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [227, 228, 229, 230, 231, 232, 233, 234, 235, 236]
2025-08-17 21:59:02,894 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 21:59:02,894 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [237, 238, 239, 240, 241, 242, 243, 244, 245, 246]
2025-08-17 21:59:04,235 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 21:59:04,236 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [257, 258, 259, 260, 261, 262, 263, 264, 265, 266]
2025-08-17 21:59:10,620 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 21:59:10,620 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [247, 248, 249, 250, 251, 252, 253, 254, 255, 256]
2025-08-17 21:59:12,671 - 关键帧分析器 - ERROR - 第3次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 21:59:12,671 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [217, 218, 219, 220, 221, 222, 223, 224, 225, 226] 分析失败
2025-08-17 21:59:12,671 - 关键帧分析器 - ERROR - 第 20 批API分析失败
2025-08-17 21:59:12,671 - 关键帧分析器 - INFO - 线程处理第 25 批: scene [267, 268, 269, 270, 271, 272, 273, 274, 275, 276]
2025-08-17 21:59:12,699 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [267, 268, 269, 270, 271, 272, 273, 274, 275, 276]
2025-08-17 21:59:18,281 - 关键帧分析器 - ERROR - 第3次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 21:59:18,282 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [227, 228, 229, 230, 231, 232, 233, 234, 235, 236] 分析失败
2025-08-17 21:59:18,282 - 关键帧分析器 - ERROR - 第 21 批API分析失败
2025-08-17 21:59:18,282 - 关键帧分析器 - INFO - 线程处理第 26 批: scene [277, 278, 279, 280, 281, 282, 283, 284, 285, 286]
2025-08-17 21:59:18,324 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [277, 278, 279, 280, 281, 282, 283, 284, 285, 286]
2025-08-17 21:59:19,934 - 关键帧分析器 - ERROR - 第3次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 21:59:19,935 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [237, 238, 239, 240, 241, 242, 243, 244, 245, 246] 分析失败
2025-08-17 21:59:19,935 - 关键帧分析器 - ERROR - 第 22 批API分析失败
2025-08-17 21:59:19,935 - 关键帧分析器 - INFO - 线程处理第 27 批: scene [287, 288, 289, 290, 291, 292, 293, 294, 295, 296]
2025-08-17 21:59:19,977 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [287, 288, 289, 290, 291, 292, 293, 294, 295, 296]
2025-08-17 21:59:25,936 - 关键帧分析器 - ERROR - 第3次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 21:59:25,936 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [257, 258, 259, 260, 261, 262, 263, 264, 265, 266] 分析失败
2025-08-17 21:59:25,936 - 关键帧分析器 - ERROR - 第 24 批API分析失败
2025-08-17 21:59:25,936 - 关键帧分析器 - INFO - 线程处理第 28 批: scene [297, 298, 299, 300, 301, 302, 303, 304, 305, 306]
2025-08-17 21:59:25,974 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [297, 298, 299, 300, 301, 302, 303, 304, 305, 306]
2025-08-17 21:59:27,230 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 21:59:27,231 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [267, 268, 269, 270, 271, 272, 273, 274, 275, 276]
2025-08-17 21:59:33,289 - 关键帧分析器 - ERROR - 第3次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 21:59:33,289 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [247, 248, 249, 250, 251, 252, 253, 254, 255, 256] 分析失败
2025-08-17 21:59:33,289 - 关键帧分析器 - ERROR - 第 23 批API分析失败
2025-08-17 21:59:33,289 - 关键帧分析器 - INFO - 线程处理第 29 批: scene [307, 308, 309, 310, 311, 312, 313, 314, 315, 316]
2025-08-17 21:59:33,336 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [307, 308, 309, 310, 311, 312, 313, 314, 315, 316]
2025-08-17 21:59:34,509 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 21:59:34,510 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [277, 278, 279, 280, 281, 282, 283, 284, 285, 286]
2025-08-17 21:59:41,115 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 21:59:41,115 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [297, 298, 299, 300, 301, 302, 303, 304, 305, 306]
2025-08-17 21:59:44,153 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 21:59:44,154 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [287, 288, 289, 290, 291, 292, 293, 294, 295, 296]
2025-08-17 21:59:46,383 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 21:59:46,383 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [267, 268, 269, 270, 271, 272, 273, 274, 275, 276]
2025-08-17 21:59:49,970 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 21:59:49,970 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [307, 308, 309, 310, 311, 312, 313, 314, 315, 316]
2025-08-17 21:59:52,063 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 21:59:52,063 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [277, 278, 279, 280, 281, 282, 283, 284, 285, 286]
2025-08-17 21:59:57,440 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 21:59:57,440 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [297, 298, 299, 300, 301, 302, 303, 304, 305, 306]
2025-08-17 22:00:05,650 - 关键帧分析器 - ERROR - 第3次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:00:05,651 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [277, 278, 279, 280, 281, 282, 283, 284, 285, 286] 分析失败
2025-08-17 22:00:05,651 - 关键帧分析器 - ERROR - 第 26 批API分析失败
2025-08-17 22:00:05,651 - 关键帧分析器 - INFO - 线程处理第 30 批: scene [317, 318, 319, 320, 321, 322, 323, 324, 325, 326]
2025-08-17 22:00:05,695 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [317, 318, 319, 320, 321, 322, 323, 324, 325, 326]
2025-08-17 22:00:08,181 - 关键帧分析器 - ERROR - 第3次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:00:08,181 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [267, 268, 269, 270, 271, 272, 273, 274, 275, 276] 分析失败
2025-08-17 22:00:08,181 - 关键帧分析器 - ERROR - 第 25 批API分析失败
2025-08-17 22:00:08,181 - 关键帧分析器 - INFO - 线程处理第 31 批: scene [327, 328, 329, 330, 331, 332, 333, 334, 335, 336]
2025-08-17 22:00:08,226 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [327, 328, 329, 330, 331, 332, 333, 334, 335, 336]
2025-08-17 22:00:11,949 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:00:11,950 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [307, 308, 309, 310, 311, 312, 313, 314, 315, 316]
2025-08-17 22:00:12,681 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:00:12,681 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [287, 288, 289, 290, 291, 292, 293, 294, 295, 296]
2025-08-17 22:00:15,016 - 关键帧分析器 - ERROR - 第3次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:00:15,017 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [297, 298, 299, 300, 301, 302, 303, 304, 305, 306] 分析失败
2025-08-17 22:00:15,017 - 关键帧分析器 - ERROR - 第 28 批API分析失败
2025-08-17 22:00:15,017 - 关键帧分析器 - INFO - 线程处理第 32 批: scene [337, 338, 339, 340, 341, 342, 343, 344, 345, 346]
2025-08-17 22:00:15,044 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [337, 338, 339, 340, 341, 342, 343, 344, 345, 346]
2025-08-17 22:00:26,484 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:00:26,485 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [327, 328, 329, 330, 331, 332, 333, 334, 335, 336]
2025-08-17 22:00:28,908 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:00:28,909 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [317, 318, 319, 320, 321, 322, 323, 324, 325, 326]
2025-08-17 22:00:31,226 - 关键帧分析器 - ERROR - 第3次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:00:31,226 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [307, 308, 309, 310, 311, 312, 313, 314, 315, 316] 分析失败
2025-08-17 22:00:31,226 - 关键帧分析器 - ERROR - 第 29 批API分析失败
2025-08-17 22:00:31,226 - 关键帧分析器 - INFO - 线程处理第 33 批: scene [347, 348, 349, 350, 351, 352, 353, 354, 355, 356]
2025-08-17 22:00:31,260 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [347, 348, 349, 350, 351, 352, 353, 354, 355, 356]
2025-08-17 22:00:34,587 - 关键帧分析器 - ERROR - 第3次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:00:34,587 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [287, 288, 289, 290, 291, 292, 293, 294, 295, 296] 分析失败
2025-08-17 22:00:34,587 - 关键帧分析器 - ERROR - 第 27 批API分析失败
2025-08-17 22:00:34,587 - 关键帧分析器 - INFO - 线程处理第 34 批: scene [357, 358, 359, 360, 361, 362, 363, 364, 365, 366]
2025-08-17 22:00:34,618 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [357, 358, 359, 360, 361, 362, 363, 364, 365, 366]
2025-08-17 22:00:42,954 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:00:42,954 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [337, 338, 339, 340, 341, 342, 343, 344, 345, 346]
2025-08-17 22:00:51,029 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:00:51,029 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [327, 328, 329, 330, 331, 332, 333, 334, 335, 336]
2025-08-17 22:00:54,910 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:00:54,910 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [357, 358, 359, 360, 361, 362, 363, 364, 365, 366]
2025-08-17 22:00:55,189 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:00:55,189 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [317, 318, 319, 320, 321, 322, 323, 324, 325, 326]
2025-08-17 22:02:33,107 - 关键帧分析器 - INFO - ============================================================
2025-08-17 22:02:33,107 - 关键帧分析器 - INFO - 视频关键帧分析工具启动
2025-08-17 22:02:33,107 - 关键帧分析器 - INFO - 每批处理图片数量: 10
2025-08-17 22:02:33,107 - 关键帧分析器 - INFO - 最大重试次数: 3
2025-08-17 22:02:33,107 - 关键帧分析器 - INFO - 并行线程数: 5
2025-08-17 22:02:33,107 - 关键帧分析器 - INFO - ============================================================
2025-08-17 22:02:33,107 - 关键帧分析器 - INFO - 检查scenes_report.json文件...
2025-08-17 22:02:33,148 - 关键帧分析器 - INFO - 成功加载scenes_report.json，包含 3221 个场景
2025-08-17 22:02:33,150 - 关键帧分析器 - INFO - 开始扫描目录: F:\github\aicut_auto\ai-video-splitter\Keyframes
2025-08-17 22:02:33,155 - 关键帧分析器 - INFO - 找到 3221 个关键帧图片文件
2025-08-17 22:02:33,155 - 关键帧分析器 - INFO - 开始按scene编号排序图片文件
2025-08-17 22:02:33,162 - 关键帧分析器 - INFO - 成功排序 3221 个图片文件
2025-08-17 22:02:33,162 - 关键帧分析器 - INFO - Scene编号范围: 1 - 3221
2025-08-17 22:02:33,162 - 关键帧分析器 - INFO - 检查scenes_report.json中已有的observation...
2025-08-17 22:02:33,163 - 关键帧分析器 - INFO - 总共 3221 个scene
2025-08-17 22:02:33,163 - 关键帧分析器 - INFO - 已有observation的scene: 26 个
2025-08-17 22:02:33,163 - 关键帧分析器 - INFO - 需要处理的scene: 3195 个
2025-08-17 22:02:33,163 - 关键帧分析器 - INFO - 跳过的scene_id: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]...
2025-08-17 22:02:33,163 - 关键帧分析器 - INFO - 准备处理 3195 张图片
2025-08-17 22:02:33,163 - 关键帧分析器 - INFO - 开始并行分批处理 3195 张图片，共 320 批，每批 10 张
2025-08-17 22:02:33,163 - 关键帧分析器 - INFO - 使用 5 个并行线程
2025-08-17 22:02:33,168 - 关键帧分析器 - INFO - 线程处理第 1 批: scene [15, 16, 29, 30, 31, 32, 33, 34, 35, 36]
2025-08-17 22:02:33,168 - 关键帧分析器 - INFO - 线程处理第 2 批: scene [37, 38, 39, 40, 41, 42, 43, 44, 45, 46]
2025-08-17 22:02:33,168 - 关键帧分析器 - INFO - 线程处理第 3 批: scene [47, 48, 49, 50, 51, 52, 53, 54, 55, 56]
2025-08-17 22:02:33,169 - 关键帧分析器 - INFO - 线程处理第 4 批: scene [57, 58, 59, 60, 61, 62, 63, 64, 65, 66]
2025-08-17 22:02:33,170 - 关键帧分析器 - INFO - 线程处理第 5 批: scene [67, 68, 69, 70, 71, 72, 73, 74, 75, 76]
2025-08-17 22:02:33,247 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [47, 48, 49, 50, 51, 52, 53, 54, 55, 56]
2025-08-17 22:02:33,248 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [15, 16, 29, 30, 31, 32, 33, 34, 35, 36]
2025-08-17 22:02:33,252 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [37, 38, 39, 40, 41, 42, 43, 44, 45, 46]
2025-08-17 22:02:33,253 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [67, 68, 69, 70, 71, 72, 73, 74, 75, 76]
2025-08-17 22:02:33,257 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [57, 58, 59, 60, 61, 62, 63, 64, 65, 66]
2025-08-17 22:02:41,527 - 关键帧分析器 - ERROR - 第1次尝试: API返回的choices数组为空
2025-08-17 22:02:41,527 - 关键帧分析器 - ERROR - 完整API响应: {'id': 'chatcmpl-c49f85dc-a889-4cd5-9c40-4ba9da201ddc', 'object': 'chat.completion', 'created': 1755439361, 'model': 'gemini-2.5-flash-preview-05-20', 'choices': [], 'usage': {'prompt_tokens': 2831, 'completion_tokens': 0, 'total_tokens': 2831}}
2025-08-17 22:02:41,527 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [15, 16, 29, 30, 31, 32, 33, 34, 35, 36]
2025-08-17 22:02:41,546 - 关键帧分析器 - ERROR - 第1次尝试: API返回的choices数组为空
2025-08-17 22:02:41,546 - 关键帧分析器 - ERROR - 完整API响应: {'id': 'chatcmpl-3d2bd2c2-416b-43f8-b304-0e17cc2b79a5', 'object': 'chat.completion', 'created': 1755439361, 'model': 'gemini-2.5-flash-preview-05-20', 'choices': [], 'usage': {'prompt_tokens': 2831, 'completion_tokens': 0, 'total_tokens': 2831}}
2025-08-17 22:02:41,546 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [47, 48, 49, 50, 51, 52, 53, 54, 55, 56]
2025-08-17 22:02:47,572 - 关键帧分析器 - ERROR - 第2次尝试: API返回的choices数组为空
2025-08-17 22:02:47,572 - 关键帧分析器 - ERROR - 完整API响应: {'id': 'chatcmpl-e8d66921-6223-4265-b412-80f9106b71c6', 'object': 'chat.completion', 'created': 1755439367, 'model': 'gemini-2.5-flash-preview-05-20', 'choices': [], 'usage': {'prompt_tokens': 2831, 'completion_tokens': 0, 'total_tokens': 2831}}
2025-08-17 22:02:47,572 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [47, 48, 49, 50, 51, 52, 53, 54, 55, 56]
2025-08-17 22:02:49,115 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 22:02:49,115 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 22:02:49,115 - 关键帧分析器 - INFO - 已更新scene_id 57 的observation
2025-08-17 22:02:49,115 - 关键帧分析器 - INFO - 已更新scene_id 58 的observation
2025-08-17 22:02:49,115 - 关键帧分析器 - INFO - 已更新scene_id 59 的observation
2025-08-17 22:02:49,115 - 关键帧分析器 - INFO - 已更新scene_id 60 的observation
2025-08-17 22:02:49,115 - 关键帧分析器 - INFO - 已更新scene_id 61 的observation
2025-08-17 22:02:49,116 - 关键帧分析器 - INFO - 已更新scene_id 62 的observation
2025-08-17 22:02:49,116 - 关键帧分析器 - INFO - 已更新scene_id 63 的observation
2025-08-17 22:02:49,116 - 关键帧分析器 - INFO - 已更新scene_id 64 的observation
2025-08-17 22:02:49,116 - 关键帧分析器 - INFO - 已更新scene_id 65 的observation
2025-08-17 22:02:49,116 - 关键帧分析器 - INFO - 已更新scene_id 66 的observation
2025-08-17 22:02:49,116 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 22:02:49,116 - 关键帧分析器 - INFO - 第 4 批处理成功
2025-08-17 22:02:49,116 - 关键帧分析器 - INFO - 线程处理第 6 批: scene [77, 78, 79, 80, 81, 82, 83, 84, 85, 86]
2025-08-17 22:02:49,135 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [77, 78, 79, 80, 81, 82, 83, 84, 85, 86]
2025-08-17 22:02:51,860 - 关键帧分析器 - ERROR - 第3次尝试: API返回的choices数组为空
2025-08-17 22:02:51,860 - 关键帧分析器 - ERROR - 完整API响应: {'id': 'chatcmpl-d24946a4-bbf5-49fa-abf0-66827e941cea', 'object': 'chat.completion', 'created': 1755439371, 'model': 'gemini-2.5-flash-preview-05-20', 'choices': [], 'usage': {'prompt_tokens': 2831, 'completion_tokens': 0, 'total_tokens': 2831}}
2025-08-17 22:02:51,860 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [47, 48, 49, 50, 51, 52, 53, 54, 55, 56] 分析失败
2025-08-17 22:02:51,860 - 关键帧分析器 - ERROR - 第 3 批API分析失败
2025-08-17 22:02:51,860 - 关键帧分析器 - INFO - 线程处理第 7 批: scene [87, 88, 89, 90, 91, 92, 93, 94, 95, 96]
2025-08-17 22:02:51,877 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [87, 88, 89, 90, 91, 92, 93, 94, 95, 96]
2025-08-17 22:02:56,693 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 22:02:56,693 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 22:02:56,693 - 关键帧分析器 - INFO - 已更新scene_id 37 的observation
2025-08-17 22:02:56,693 - 关键帧分析器 - INFO - 已更新scene_id 38 的observation
2025-08-17 22:02:56,693 - 关键帧分析器 - INFO - 已更新scene_id 39 的observation
2025-08-17 22:02:56,693 - 关键帧分析器 - INFO - 已更新scene_id 40 的observation
2025-08-17 22:02:56,693 - 关键帧分析器 - INFO - 已更新scene_id 41 的observation
2025-08-17 22:02:56,693 - 关键帧分析器 - INFO - 已更新scene_id 42 的observation
2025-08-17 22:02:56,693 - 关键帧分析器 - INFO - 已更新scene_id 43 的observation
2025-08-17 22:02:56,694 - 关键帧分析器 - INFO - 已更新scene_id 44 的observation
2025-08-17 22:02:56,694 - 关键帧分析器 - INFO - 已更新scene_id 45 的observation
2025-08-17 22:02:56,694 - 关键帧分析器 - INFO - 已更新scene_id 46 的observation
2025-08-17 22:02:56,694 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 22:02:56,694 - 关键帧分析器 - INFO - 第 2 批处理成功
2025-08-17 22:02:56,694 - 关键帧分析器 - INFO - 线程处理第 8 批: scene [97, 98, 99, 100, 101, 102, 103, 104, 105, 106]
2025-08-17 22:02:56,703 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [97, 98, 99, 100, 101, 102, 103, 104, 105, 106]
2025-08-17 22:02:57,156 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 22:02:57,156 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 22:02:57,157 - 关键帧分析器 - INFO - 已更新scene_id 67 的observation
2025-08-17 22:02:57,157 - 关键帧分析器 - INFO - 已更新scene_id 68 的observation
2025-08-17 22:02:57,157 - 关键帧分析器 - INFO - 已更新scene_id 69 的observation
2025-08-17 22:02:57,157 - 关键帧分析器 - INFO - 已更新scene_id 70 的observation
2025-08-17 22:02:57,157 - 关键帧分析器 - INFO - 已更新scene_id 71 的observation
2025-08-17 22:02:57,157 - 关键帧分析器 - INFO - 已更新scene_id 72 的observation
2025-08-17 22:02:57,157 - 关键帧分析器 - INFO - 已更新scene_id 73 的observation
2025-08-17 22:02:57,157 - 关键帧分析器 - INFO - 已更新scene_id 74 的observation
2025-08-17 22:02:57,157 - 关键帧分析器 - INFO - 已更新scene_id 75 的observation
2025-08-17 22:02:57,157 - 关键帧分析器 - INFO - 已更新scene_id 76 的observation
2025-08-17 22:02:57,157 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 22:02:57,157 - 关键帧分析器 - INFO - 第 5 批处理成功
2025-08-17 22:02:57,157 - 关键帧分析器 - INFO - 线程处理第 9 批: scene [107, 108, 109, 110, 111, 112, 113, 114, 115, 116]
2025-08-17 22:02:57,173 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [107, 108, 109, 110, 111, 112, 113, 114, 115, 116]
2025-08-17 22:02:57,807 - 关键帧分析器 - ERROR - 第2次尝试: API返回的choices数组为空
2025-08-17 22:02:57,807 - 关键帧分析器 - ERROR - 完整API响应: {'id': 'chatcmpl-e2fb4f8b-2457-4973-8036-746aead6f6b1', 'object': 'chat.completion', 'created': 1755439377, 'model': 'gemini-2.5-flash-preview-05-20', 'choices': [], 'usage': {'prompt_tokens': 2831, 'completion_tokens': 0, 'total_tokens': 2831}}
2025-08-17 22:02:57,807 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [15, 16, 29, 30, 31, 32, 33, 34, 35, 36]
2025-08-17 22:03:01,887 - 关键帧分析器 - ERROR - 第3次尝试: API返回的choices数组为空
2025-08-17 22:03:01,887 - 关键帧分析器 - ERROR - 完整API响应: {'id': 'chatcmpl-09ec7cf6-efe9-4197-a2fd-053c77912dbf', 'object': 'chat.completion', 'created': 1755439381, 'model': 'gemini-2.5-flash-preview-05-20', 'choices': [], 'usage': {'prompt_tokens': 2831, 'completion_tokens': 0, 'total_tokens': 2831}}
2025-08-17 22:03:01,887 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [15, 16, 29, 30, 31, 32, 33, 34, 35, 36] 分析失败
2025-08-17 22:03:01,887 - 关键帧分析器 - ERROR - 第 1 批API分析失败
2025-08-17 22:03:01,887 - 关键帧分析器 - INFO - 线程处理第 10 批: scene [117, 118, 119, 120, 121, 122, 123, 124, 125, 126]
2025-08-17 22:03:01,904 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [117, 118, 119, 120, 121, 122, 123, 124, 125, 126]
2025-08-17 22:03:06,455 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 22:03:06,455 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 22:03:06,455 - 关键帧分析器 - INFO - 已更新scene_id 77 的observation
2025-08-17 22:03:06,455 - 关键帧分析器 - INFO - 已更新scene_id 78 的observation
2025-08-17 22:03:06,455 - 关键帧分析器 - INFO - 已更新scene_id 79 的observation
2025-08-17 22:03:06,455 - 关键帧分析器 - INFO - 已更新scene_id 80 的observation
2025-08-17 22:03:06,455 - 关键帧分析器 - INFO - 已更新scene_id 81 的observation
2025-08-17 22:03:06,455 - 关键帧分析器 - INFO - 已更新scene_id 82 的observation
2025-08-17 22:03:06,455 - 关键帧分析器 - INFO - 已更新scene_id 83 的observation
2025-08-17 22:03:06,455 - 关键帧分析器 - INFO - 已更新scene_id 84 的observation
2025-08-17 22:03:06,455 - 关键帧分析器 - INFO - 已更新scene_id 85 的observation
2025-08-17 22:03:06,455 - 关键帧分析器 - INFO - 已更新scene_id 86 的observation
2025-08-17 22:03:06,455 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 22:03:06,455 - 关键帧分析器 - INFO - 第 6 批处理成功
2025-08-17 22:03:06,455 - 关键帧分析器 - INFO - 线程处理第 11 批: scene [127, 128, 129, 130, 131, 132, 133, 134, 135, 136]
2025-08-17 22:03:06,471 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [127, 128, 129, 130, 131, 132, 133, 134, 135, 136]
2025-08-17 22:03:10,037 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 22:03:10,037 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 22:03:10,037 - 关键帧分析器 - INFO - 已更新scene_id 87 的observation
2025-08-17 22:03:10,037 - 关键帧分析器 - INFO - 已更新scene_id 88 的observation
2025-08-17 22:03:10,037 - 关键帧分析器 - INFO - 已更新scene_id 89 的observation
2025-08-17 22:03:10,037 - 关键帧分析器 - INFO - 已更新scene_id 90 的observation
2025-08-17 22:03:10,037 - 关键帧分析器 - INFO - 已更新scene_id 91 的observation
2025-08-17 22:03:10,037 - 关键帧分析器 - INFO - 已更新scene_id 92 的observation
2025-08-17 22:03:10,039 - 关键帧分析器 - INFO - 已更新scene_id 93 的observation
2025-08-17 22:03:10,039 - 关键帧分析器 - INFO - 已更新scene_id 94 的observation
2025-08-17 22:03:10,039 - 关键帧分析器 - INFO - 已更新scene_id 95 的observation
2025-08-17 22:03:10,039 - 关键帧分析器 - INFO - 已更新scene_id 96 的observation
2025-08-17 22:03:10,039 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 22:03:10,039 - 关键帧分析器 - INFO - 第 7 批处理成功
2025-08-17 22:03:10,039 - 关键帧分析器 - INFO - 线程处理第 12 批: scene [137, 138, 139, 140, 141, 142, 143, 144, 145, 146]
2025-08-17 22:03:10,053 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [137, 138, 139, 140, 141, 142, 143, 144, 145, 146]
2025-08-17 22:03:11,997 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 22:03:11,997 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 22:03:11,997 - 关键帧分析器 - INFO - 已更新scene_id 97 的observation
2025-08-17 22:03:11,997 - 关键帧分析器 - INFO - 已更新scene_id 98 的observation
2025-08-17 22:03:11,997 - 关键帧分析器 - INFO - 已更新scene_id 99 的observation
2025-08-17 22:03:11,997 - 关键帧分析器 - INFO - 已更新scene_id 100 的observation
2025-08-17 22:03:11,997 - 关键帧分析器 - INFO - 已更新scene_id 101 的observation
2025-08-17 22:03:11,998 - 关键帧分析器 - INFO - 已更新scene_id 102 的observation
2025-08-17 22:03:11,998 - 关键帧分析器 - INFO - 已更新scene_id 103 的observation
2025-08-17 22:03:11,998 - 关键帧分析器 - INFO - 已更新scene_id 104 的observation
2025-08-17 22:03:11,998 - 关键帧分析器 - INFO - 已更新scene_id 105 的observation
2025-08-17 22:03:11,998 - 关键帧分析器 - INFO - 已更新scene_id 106 的observation
2025-08-17 22:03:11,999 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 22:03:11,999 - 关键帧分析器 - INFO - 第 8 批处理成功
2025-08-17 22:03:11,999 - 关键帧分析器 - INFO - 线程处理第 13 批: scene [147, 148, 149, 150, 151, 152, 153, 154, 155, 156]
2025-08-17 22:03:12,025 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [147, 148, 149, 150, 151, 152, 153, 154, 155, 156]
2025-08-17 22:03:13,852 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 22:03:13,852 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 22:03:13,852 - 关键帧分析器 - INFO - 已更新scene_id 117 的observation
2025-08-17 22:03:13,852 - 关键帧分析器 - INFO - 已更新scene_id 118 的observation
2025-08-17 22:03:13,852 - 关键帧分析器 - INFO - 已更新scene_id 119 的observation
2025-08-17 22:03:13,852 - 关键帧分析器 - INFO - 已更新scene_id 120 的observation
2025-08-17 22:03:13,852 - 关键帧分析器 - INFO - 已更新scene_id 121 的observation
2025-08-17 22:03:13,852 - 关键帧分析器 - INFO - 已更新scene_id 122 的observation
2025-08-17 22:03:13,853 - 关键帧分析器 - INFO - 已更新scene_id 123 的observation
2025-08-17 22:03:13,853 - 关键帧分析器 - INFO - 已更新scene_id 124 的observation
2025-08-17 22:03:13,853 - 关键帧分析器 - INFO - 已更新scene_id 125 的observation
2025-08-17 22:03:13,853 - 关键帧分析器 - INFO - 已更新scene_id 126 的observation
2025-08-17 22:03:13,853 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 22:03:13,853 - 关键帧分析器 - INFO - 第 10 批处理成功
2025-08-17 22:03:13,853 - 关键帧分析器 - INFO - 线程处理第 14 批: scene [157, 158, 159, 160, 161, 162, 163, 164, 165, 166]
2025-08-17 22:03:13,873 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [157, 158, 159, 160, 161, 162, 163, 164, 165, 166]
2025-08-17 22:03:17,254 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 22:03:17,254 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 22:03:17,254 - 关键帧分析器 - INFO - 已更新scene_id 107 的observation
2025-08-17 22:03:17,254 - 关键帧分析器 - INFO - 已更新scene_id 108 的observation
2025-08-17 22:03:17,254 - 关键帧分析器 - INFO - 已更新scene_id 109 的observation
2025-08-17 22:03:17,254 - 关键帧分析器 - INFO - 已更新scene_id 110 的observation
2025-08-17 22:03:17,254 - 关键帧分析器 - INFO - 已更新scene_id 111 的observation
2025-08-17 22:03:17,254 - 关键帧分析器 - INFO - 已更新scene_id 112 的observation
2025-08-17 22:03:17,255 - 关键帧分析器 - INFO - 已更新scene_id 113 的observation
2025-08-17 22:03:17,255 - 关键帧分析器 - INFO - 已更新scene_id 114 的observation
2025-08-17 22:03:17,255 - 关键帧分析器 - INFO - 已更新scene_id 115 的observation
2025-08-17 22:03:17,255 - 关键帧分析器 - INFO - 已更新scene_id 116 的observation
2025-08-17 22:03:17,255 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 22:03:17,255 - 关键帧分析器 - INFO - 第 9 批处理成功
2025-08-17 22:03:17,255 - 关键帧分析器 - INFO - 线程处理第 15 批: scene [167, 168, 169, 170, 171, 172, 173, 174, 175, 176]
2025-08-17 22:03:17,276 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [167, 168, 169, 170, 171, 172, 173, 174, 175, 176]
2025-08-17 22:03:20,630 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 22:03:20,630 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 22:03:20,630 - 关键帧分析器 - INFO - 已更新scene_id 127 的observation
2025-08-17 22:03:20,630 - 关键帧分析器 - INFO - 已更新scene_id 128 的observation
2025-08-17 22:03:20,630 - 关键帧分析器 - INFO - 已更新scene_id 129 的observation
2025-08-17 22:03:20,631 - 关键帧分析器 - INFO - 已更新scene_id 130 的observation
2025-08-17 22:03:20,631 - 关键帧分析器 - INFO - 已更新scene_id 131 的observation
2025-08-17 22:03:20,631 - 关键帧分析器 - INFO - 已更新scene_id 132 的observation
2025-08-17 22:03:20,631 - 关键帧分析器 - INFO - 已更新scene_id 133 的observation
2025-08-17 22:03:20,631 - 关键帧分析器 - INFO - 已更新scene_id 134 的observation
2025-08-17 22:03:20,631 - 关键帧分析器 - INFO - 已更新scene_id 135 的observation
2025-08-17 22:03:20,631 - 关键帧分析器 - INFO - 已更新scene_id 136 的observation
2025-08-17 22:03:20,631 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 22:03:20,631 - 关键帧分析器 - INFO - 第 11 批处理成功
2025-08-17 22:03:20,631 - 关键帧分析器 - INFO - 线程处理第 16 批: scene [177, 178, 179, 180, 181, 182, 183, 184, 185, 186]
2025-08-17 22:03:20,652 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [177, 178, 179, 180, 181, 182, 183, 184, 185, 186]
2025-08-17 22:03:22,981 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 22:03:22,981 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 22:03:22,981 - 关键帧分析器 - INFO - 已更新scene_id 137 的observation
2025-08-17 22:03:22,981 - 关键帧分析器 - INFO - 已更新scene_id 138 的observation
2025-08-17 22:03:22,981 - 关键帧分析器 - INFO - 已更新scene_id 139 的observation
2025-08-17 22:03:22,981 - 关键帧分析器 - INFO - 已更新scene_id 140 的observation
2025-08-17 22:03:22,981 - 关键帧分析器 - INFO - 已更新scene_id 141 的observation
2025-08-17 22:03:22,982 - 关键帧分析器 - INFO - 已更新scene_id 142 的observation
2025-08-17 22:03:22,982 - 关键帧分析器 - INFO - 已更新scene_id 143 的observation
2025-08-17 22:03:22,982 - 关键帧分析器 - INFO - 已更新scene_id 144 的observation
2025-08-17 22:03:22,982 - 关键帧分析器 - INFO - 已更新scene_id 145 的observation
2025-08-17 22:03:22,982 - 关键帧分析器 - INFO - 已更新scene_id 146 的observation
2025-08-17 22:03:22,982 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 22:03:22,982 - 关键帧分析器 - INFO - 第 12 批处理成功
2025-08-17 22:03:22,982 - 关键帧分析器 - INFO - 线程处理第 17 批: scene [187, 188, 189, 190, 191, 192, 193, 194, 195, 196]
2025-08-17 22:03:23,004 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [187, 188, 189, 190, 191, 192, 193, 194, 195, 196]
2025-08-17 22:03:26,029 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 22:03:26,029 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 22:03:26,029 - 关键帧分析器 - INFO - 已更新scene_id 147 的observation
2025-08-17 22:03:26,029 - 关键帧分析器 - INFO - 已更新scene_id 148 的observation
2025-08-17 22:03:26,029 - 关键帧分析器 - INFO - 已更新scene_id 149 的observation
2025-08-17 22:03:26,029 - 关键帧分析器 - INFO - 已更新scene_id 150 的observation
2025-08-17 22:03:26,029 - 关键帧分析器 - INFO - 已更新scene_id 151 的observation
2025-08-17 22:03:26,029 - 关键帧分析器 - INFO - 已更新scene_id 152 的observation
2025-08-17 22:03:26,029 - 关键帧分析器 - INFO - 已更新scene_id 153 的observation
2025-08-17 22:03:26,029 - 关键帧分析器 - INFO - 已更新scene_id 154 的observation
2025-08-17 22:03:26,030 - 关键帧分析器 - INFO - 已更新scene_id 155 的observation
2025-08-17 22:03:26,030 - 关键帧分析器 - INFO - 已更新scene_id 156 的observation
2025-08-17 22:03:26,030 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 22:03:26,030 - 关键帧分析器 - INFO - 第 13 批处理成功
2025-08-17 22:03:26,030 - 关键帧分析器 - INFO - 线程处理第 18 批: scene [197, 198, 199, 200, 201, 202, 203, 204, 205, 206]
2025-08-17 22:03:26,053 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [197, 198, 199, 200, 201, 202, 203, 204, 205, 206]
2025-08-17 22:03:31,598 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 22:03:31,598 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 22:03:31,598 - 关键帧分析器 - INFO - 已更新scene_id 167 的observation
2025-08-17 22:03:31,598 - 关键帧分析器 - INFO - 已更新scene_id 168 的observation
2025-08-17 22:03:31,599 - 关键帧分析器 - INFO - 已更新scene_id 169 的observation
2025-08-17 22:03:31,599 - 关键帧分析器 - INFO - 已更新scene_id 170 的observation
2025-08-17 22:03:31,599 - 关键帧分析器 - INFO - 已更新scene_id 171 的observation
2025-08-17 22:03:31,599 - 关键帧分析器 - INFO - 已更新scene_id 172 的observation
2025-08-17 22:03:31,599 - 关键帧分析器 - INFO - 已更新scene_id 173 的observation
2025-08-17 22:03:31,599 - 关键帧分析器 - INFO - 已更新scene_id 174 的observation
2025-08-17 22:03:31,599 - 关键帧分析器 - INFO - 已更新scene_id 175 的observation
2025-08-17 22:03:31,599 - 关键帧分析器 - INFO - 已更新scene_id 176 的observation
2025-08-17 22:03:31,599 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 22:03:31,600 - 关键帧分析器 - INFO - 第 15 批处理成功
2025-08-17 22:03:31,600 - 关键帧分析器 - INFO - 线程处理第 19 批: scene [207, 208, 209, 210, 211, 212, 213, 214, 215, 216]
2025-08-17 22:03:31,620 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [207, 208, 209, 210, 211, 212, 213, 214, 215, 216]
2025-08-17 22:03:32,073 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 22:03:32,073 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 22:03:32,073 - 关键帧分析器 - INFO - 已更新scene_id 177 的observation
2025-08-17 22:03:32,073 - 关键帧分析器 - INFO - 已更新scene_id 178 的observation
2025-08-17 22:03:32,073 - 关键帧分析器 - INFO - 已更新scene_id 179 的observation
2025-08-17 22:03:32,073 - 关键帧分析器 - INFO - 已更新scene_id 180 的observation
2025-08-17 22:03:32,073 - 关键帧分析器 - INFO - 已更新scene_id 181 的observation
2025-08-17 22:03:32,073 - 关键帧分析器 - INFO - 已更新scene_id 182 的observation
2025-08-17 22:03:32,073 - 关键帧分析器 - INFO - 已更新scene_id 183 的observation
2025-08-17 22:03:32,074 - 关键帧分析器 - INFO - 已更新scene_id 184 的observation
2025-08-17 22:03:32,074 - 关键帧分析器 - INFO - 已更新scene_id 185 的observation
2025-08-17 22:03:32,074 - 关键帧分析器 - INFO - 已更新scene_id 186 的observation
2025-08-17 22:03:32,074 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 22:03:32,074 - 关键帧分析器 - INFO - 第 16 批处理成功
2025-08-17 22:03:32,074 - 关键帧分析器 - INFO - 线程处理第 20 批: scene [217, 218, 219, 220, 221, 222, 223, 224, 225, 226]
2025-08-17 22:03:32,092 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [217, 218, 219, 220, 221, 222, 223, 224, 225, 226]
2025-08-17 22:03:34,019 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 22:03:34,019 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 22:03:34,019 - 关键帧分析器 - INFO - 已更新scene_id 157 的observation
2025-08-17 22:03:34,019 - 关键帧分析器 - INFO - 已更新scene_id 158 的observation
2025-08-17 22:03:34,019 - 关键帧分析器 - INFO - 已更新scene_id 159 的observation
2025-08-17 22:03:34,019 - 关键帧分析器 - INFO - 已更新scene_id 160 的observation
2025-08-17 22:03:34,019 - 关键帧分析器 - INFO - 已更新scene_id 161 的observation
2025-08-17 22:03:34,019 - 关键帧分析器 - INFO - 已更新scene_id 162 的observation
2025-08-17 22:03:34,019 - 关键帧分析器 - INFO - 已更新scene_id 163 的observation
2025-08-17 22:03:34,019 - 关键帧分析器 - INFO - 已更新scene_id 164 的observation
2025-08-17 22:03:34,020 - 关键帧分析器 - INFO - 已更新scene_id 165 的observation
2025-08-17 22:03:34,020 - 关键帧分析器 - INFO - 已更新scene_id 166 的observation
2025-08-17 22:03:34,020 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 22:03:34,020 - 关键帧分析器 - INFO - 第 14 批处理成功
2025-08-17 22:03:34,020 - 关键帧分析器 - INFO - 线程处理第 21 批: scene [227, 228, 229, 230, 231, 232, 233, 234, 235, 236]
2025-08-17 22:03:34,039 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [227, 228, 229, 230, 231, 232, 233, 234, 235, 236]
2025-08-17 22:03:38,441 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 22:03:38,441 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 22:03:38,441 - 关键帧分析器 - INFO - 已更新scene_id 187 的observation
2025-08-17 22:03:38,442 - 关键帧分析器 - INFO - 已更新scene_id 188 的observation
2025-08-17 22:03:38,442 - 关键帧分析器 - INFO - 已更新scene_id 189 的observation
2025-08-17 22:03:38,442 - 关键帧分析器 - INFO - 已更新scene_id 190 的observation
2025-08-17 22:03:38,442 - 关键帧分析器 - INFO - 已更新scene_id 191 的observation
2025-08-17 22:03:38,442 - 关键帧分析器 - INFO - 已更新scene_id 192 的observation
2025-08-17 22:03:38,442 - 关键帧分析器 - INFO - 已更新scene_id 193 的observation
2025-08-17 22:03:38,443 - 关键帧分析器 - INFO - 已更新scene_id 194 的observation
2025-08-17 22:03:38,443 - 关键帧分析器 - INFO - 已更新scene_id 195 的observation
2025-08-17 22:03:38,443 - 关键帧分析器 - INFO - 已更新scene_id 196 的observation
2025-08-17 22:03:38,443 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 22:03:38,443 - 关键帧分析器 - INFO - 第 17 批处理成功
2025-08-17 22:03:38,443 - 关键帧分析器 - INFO - 线程处理第 22 批: scene [237, 238, 239, 240, 241, 242, 243, 244, 245, 246]
2025-08-17 22:03:38,466 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [237, 238, 239, 240, 241, 242, 243, 244, 245, 246]
2025-08-17 22:03:40,764 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 22:03:40,764 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 22:03:40,764 - 关键帧分析器 - INFO - 已更新scene_id 197 的observation
2025-08-17 22:03:40,764 - 关键帧分析器 - INFO - 已更新scene_id 198 的observation
2025-08-17 22:03:40,764 - 关键帧分析器 - INFO - 已更新scene_id 199 的observation
2025-08-17 22:03:40,764 - 关键帧分析器 - INFO - 已更新scene_id 200 的observation
2025-08-17 22:03:40,764 - 关键帧分析器 - INFO - 已更新scene_id 201 的observation
2025-08-17 22:03:40,764 - 关键帧分析器 - INFO - 已更新scene_id 202 的observation
2025-08-17 22:03:40,765 - 关键帧分析器 - INFO - 已更新scene_id 203 的observation
2025-08-17 22:03:40,765 - 关键帧分析器 - INFO - 已更新scene_id 204 的observation
2025-08-17 22:03:40,765 - 关键帧分析器 - INFO - 已更新scene_id 205 的observation
2025-08-17 22:03:40,765 - 关键帧分析器 - INFO - 已更新scene_id 206 的observation
2025-08-17 22:03:40,765 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 22:03:40,765 - 关键帧分析器 - INFO - 第 18 批处理成功
2025-08-17 22:03:40,765 - 关键帧分析器 - INFO - 线程处理第 23 批: scene [247, 248, 249, 250, 251, 252, 253, 254, 255, 256]
2025-08-17 22:03:40,786 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [247, 248, 249, 250, 251, 252, 253, 254, 255, 256]
2025-08-17 22:03:40,920 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:03:40,920 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [207, 208, 209, 210, 211, 212, 213, 214, 215, 216]
2025-08-17 22:03:42,678 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:03:42,679 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [217, 218, 219, 220, 221, 222, 223, 224, 225, 226]
2025-08-17 22:03:49,571 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 22:03:49,571 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 22:03:49,572 - 关键帧分析器 - INFO - 已更新scene_id 227 的observation
2025-08-17 22:03:49,572 - 关键帧分析器 - INFO - 已更新scene_id 228 的observation
2025-08-17 22:03:49,572 - 关键帧分析器 - INFO - 已更新scene_id 229 的observation
2025-08-17 22:03:49,572 - 关键帧分析器 - INFO - 已更新scene_id 230 的observation
2025-08-17 22:03:49,572 - 关键帧分析器 - INFO - 已更新scene_id 231 的observation
2025-08-17 22:03:49,573 - 关键帧分析器 - INFO - 已更新scene_id 232 的observation
2025-08-17 22:03:49,573 - 关键帧分析器 - INFO - 已更新scene_id 233 的observation
2025-08-17 22:03:49,573 - 关键帧分析器 - INFO - 已更新scene_id 234 的observation
2025-08-17 22:03:49,573 - 关键帧分析器 - INFO - 已更新scene_id 235 的observation
2025-08-17 22:03:49,573 - 关键帧分析器 - INFO - 已更新scene_id 236 的observation
2025-08-17 22:03:49,574 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 22:03:49,574 - 关键帧分析器 - INFO - 第 21 批处理成功
2025-08-17 22:03:49,574 - 关键帧分析器 - INFO - 线程处理第 24 批: scene [257, 258, 259, 260, 261, 262, 263, 264, 265, 266]
2025-08-17 22:03:49,598 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [257, 258, 259, 260, 261, 262, 263, 264, 265, 266]
2025-08-17 22:03:55,861 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:03:55,861 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [237, 238, 239, 240, 241, 242, 243, 244, 245, 246]
2025-08-17 22:03:58,403 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:03:58,404 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [207, 208, 209, 210, 211, 212, 213, 214, 215, 216]
2025-08-17 22:04:01,134 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 22:04:01,134 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 22:04:01,135 - 关键帧分析器 - INFO - 已更新scene_id 247 的observation
2025-08-17 22:04:01,135 - 关键帧分析器 - INFO - 已更新scene_id 248 的observation
2025-08-17 22:04:01,135 - 关键帧分析器 - INFO - 已更新scene_id 249 的observation
2025-08-17 22:04:01,135 - 关键帧分析器 - INFO - 已更新scene_id 250 的observation
2025-08-17 22:04:01,135 - 关键帧分析器 - INFO - 已更新scene_id 251 的observation
2025-08-17 22:04:01,135 - 关键帧分析器 - INFO - 已更新scene_id 252 的observation
2025-08-17 22:04:01,135 - 关键帧分析器 - INFO - 已更新scene_id 253 的observation
2025-08-17 22:04:01,135 - 关键帧分析器 - INFO - 已更新scene_id 254 的observation
2025-08-17 22:04:01,135 - 关键帧分析器 - INFO - 已更新scene_id 255 的observation
2025-08-17 22:04:01,135 - 关键帧分析器 - INFO - 已更新scene_id 256 的observation
2025-08-17 22:04:01,135 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 22:04:01,136 - 关键帧分析器 - INFO - 第 23 批处理成功
2025-08-17 22:04:01,136 - 关键帧分析器 - INFO - 线程处理第 25 批: scene [267, 268, 269, 270, 271, 272, 273, 274, 275, 276]
2025-08-17 22:04:01,157 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [267, 268, 269, 270, 271, 272, 273, 274, 275, 276]
2025-08-17 22:04:02,382 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:04:02,382 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [217, 218, 219, 220, 221, 222, 223, 224, 225, 226]
2025-08-17 22:04:07,475 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 22:04:07,475 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 22:04:07,475 - 关键帧分析器 - INFO - 已更新scene_id 257 的observation
2025-08-17 22:04:07,476 - 关键帧分析器 - INFO - 已更新scene_id 258 的observation
2025-08-17 22:04:07,476 - 关键帧分析器 - INFO - 已更新scene_id 259 的observation
2025-08-17 22:04:07,476 - 关键帧分析器 - INFO - 已更新scene_id 260 的observation
2025-08-17 22:04:07,476 - 关键帧分析器 - INFO - 已更新scene_id 261 的observation
2025-08-17 22:04:07,477 - 关键帧分析器 - INFO - 已更新scene_id 262 的observation
2025-08-17 22:04:07,477 - 关键帧分析器 - INFO - 已更新scene_id 263 的observation
2025-08-17 22:04:07,477 - 关键帧分析器 - INFO - 已更新scene_id 264 的observation
2025-08-17 22:04:07,477 - 关键帧分析器 - INFO - 已更新scene_id 265 的observation
2025-08-17 22:04:07,477 - 关键帧分析器 - INFO - 已更新scene_id 266 的observation
2025-08-17 22:04:07,477 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 22:04:07,477 - 关键帧分析器 - INFO - 第 24 批处理成功
2025-08-17 22:04:07,479 - 关键帧分析器 - INFO - 线程处理第 26 批: scene [277, 278, 279, 280, 281, 282, 283, 284, 285, 286]
2025-08-17 22:04:07,503 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [277, 278, 279, 280, 281, 282, 283, 284, 285, 286]
2025-08-17 22:04:08,641 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:04:08,642 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [237, 238, 239, 240, 241, 242, 243, 244, 245, 246]
2025-08-17 22:04:10,415 - 关键帧分析器 - ERROR - 第3次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:04:10,415 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [207, 208, 209, 210, 211, 212, 213, 214, 215, 216] 分析失败
2025-08-17 22:04:10,417 - 关键帧分析器 - ERROR - 第 19 批API分析失败
2025-08-17 22:04:10,417 - 关键帧分析器 - INFO - 线程处理第 27 批: scene [287, 288, 289, 290, 291, 292, 293, 294, 295, 296]
2025-08-17 22:04:10,445 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [287, 288, 289, 290, 291, 292, 293, 294, 295, 296]
2025-08-17 22:04:17,250 - 关键帧分析器 - ERROR - 第3次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:04:17,251 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [217, 218, 219, 220, 221, 222, 223, 224, 225, 226] 分析失败
2025-08-17 22:04:17,251 - 关键帧分析器 - ERROR - 第 20 批API分析失败
2025-08-17 22:04:17,251 - 关键帧分析器 - INFO - 线程处理第 28 批: scene [297, 298, 299, 300, 301, 302, 303, 304, 305, 306]
2025-08-17 22:04:17,274 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [297, 298, 299, 300, 301, 302, 303, 304, 305, 306]
2025-08-17 22:04:22,422 - 关键帧分析器 - ERROR - 第3次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:04:22,422 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [237, 238, 239, 240, 241, 242, 243, 244, 245, 246] 分析失败
2025-08-17 22:04:22,422 - 关键帧分析器 - ERROR - 第 22 批API分析失败
2025-08-17 22:04:22,423 - 关键帧分析器 - INFO - 线程处理第 29 批: scene [307, 308, 309, 310, 311, 312, 313, 314, 315, 316]
2025-08-17 22:04:22,454 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [307, 308, 309, 310, 311, 312, 313, 314, 315, 316]
2025-08-17 22:04:26,321 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:04:26,321 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [277, 278, 279, 280, 281, 282, 283, 284, 285, 286]
2025-08-17 22:04:26,863 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:04:26,863 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [287, 288, 289, 290, 291, 292, 293, 294, 295, 296]
2025-08-17 22:04:31,737 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:04:31,738 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [267, 268, 269, 270, 271, 272, 273, 274, 275, 276]
2025-08-17 22:04:34,304 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:04:34,305 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [297, 298, 299, 300, 301, 302, 303, 304, 305, 306]
2025-08-17 22:04:43,303 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:04:43,303 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [277, 278, 279, 280, 281, 282, 283, 284, 285, 286]
2025-08-17 22:04:45,068 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:04:45,075 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [307, 308, 309, 310, 311, 312, 313, 314, 315, 316]
2025-08-17 22:04:47,511 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:04:47,512 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [267, 268, 269, 270, 271, 272, 273, 274, 275, 276]
2025-08-17 22:04:49,885 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:04:49,885 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [287, 288, 289, 290, 291, 292, 293, 294, 295, 296]
2025-08-17 22:04:50,845 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:04:50,845 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [297, 298, 299, 300, 301, 302, 303, 304, 305, 306]
2025-08-17 22:04:59,464 - 关键帧分析器 - ERROR - 第3次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:04:59,465 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [277, 278, 279, 280, 281, 282, 283, 284, 285, 286] 分析失败
2025-08-17 22:04:59,465 - 关键帧分析器 - ERROR - 第 26 批API分析失败
2025-08-17 22:04:59,465 - 关键帧分析器 - INFO - 线程处理第 30 批: scene [317, 318, 319, 320, 321, 322, 323, 324, 325, 326]
2025-08-17 22:04:59,493 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [317, 318, 319, 320, 321, 322, 323, 324, 325, 326]
2025-08-17 22:05:06,930 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:05:06,931 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [307, 308, 309, 310, 311, 312, 313, 314, 315, 316]
2025-08-17 22:05:10,289 - 关键帧分析器 - ERROR - 第3次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:05:10,289 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [297, 298, 299, 300, 301, 302, 303, 304, 305, 306] 分析失败
2025-08-17 22:05:10,289 - 关键帧分析器 - ERROR - 第 28 批API分析失败
2025-08-17 22:05:10,289 - 关键帧分析器 - INFO - 线程处理第 31 批: scene [327, 328, 329, 330, 331, 332, 333, 334, 335, 336]
2025-08-17 22:05:10,319 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [327, 328, 329, 330, 331, 332, 333, 334, 335, 336]
2025-08-17 22:05:10,477 - 关键帧分析器 - ERROR - 第3次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:05:10,477 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [267, 268, 269, 270, 271, 272, 273, 274, 275, 276] 分析失败
2025-08-17 22:05:10,478 - 关键帧分析器 - ERROR - 第 25 批API分析失败
2025-08-17 22:05:10,478 - 关键帧分析器 - INFO - 线程处理第 32 批: scene [337, 338, 339, 340, 341, 342, 343, 344, 345, 346]
2025-08-17 22:05:10,500 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [337, 338, 339, 340, 341, 342, 343, 344, 345, 346]
2025-08-17 22:05:13,864 - 关键帧分析器 - ERROR - 第3次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:05:13,864 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [287, 288, 289, 290, 291, 292, 293, 294, 295, 296] 分析失败
2025-08-17 22:05:13,864 - 关键帧分析器 - ERROR - 第 27 批API分析失败
2025-08-17 22:05:13,864 - 关键帧分析器 - INFO - 线程处理第 33 批: scene [347, 348, 349, 350, 351, 352, 353, 354, 355, 356]
2025-08-17 22:05:13,891 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [347, 348, 349, 350, 351, 352, 353, 354, 355, 356]
2025-08-17 22:05:18,809 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:05:18,809 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [317, 318, 319, 320, 321, 322, 323, 324, 325, 326]
2025-08-17 22:05:29,264 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:05:29,264 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [337, 338, 339, 340, 341, 342, 343, 344, 345, 346]
2025-08-17 22:05:30,251 - 关键帧分析器 - ERROR - 第3次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:05:30,251 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [307, 308, 309, 310, 311, 312, 313, 314, 315, 316] 分析失败
2025-08-17 22:05:30,251 - 关键帧分析器 - ERROR - 第 29 批API分析失败
2025-08-17 22:05:30,251 - 关键帧分析器 - INFO - 线程处理第 34 批: scene [357, 358, 359, 360, 361, 362, 363, 364, 365, 366]
2025-08-17 22:05:30,275 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [357, 358, 359, 360, 361, 362, 363, 364, 365, 366]
2025-08-17 22:05:32,548 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:05:32,548 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [347, 348, 349, 350, 351, 352, 353, 354, 355, 356]
2025-08-17 22:05:32,760 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:05:32,760 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [327, 328, 329, 330, 331, 332, 333, 334, 335, 336]
2025-08-17 22:05:45,795 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:05:45,796 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [347, 348, 349, 350, 351, 352, 353, 354, 355, 356]
2025-08-17 22:05:48,793 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:05:48,793 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [317, 318, 319, 320, 321, 322, 323, 324, 325, 326]
2025-08-17 22:05:55,603 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:05:55,604 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [327, 328, 329, 330, 331, 332, 333, 334, 335, 336]
2025-08-17 22:05:55,607 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:05:55,635 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [357, 358, 359, 360, 361, 362, 363, 364, 365, 366]
2025-08-17 22:05:56,906 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:05:57,001 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [337, 338, 339, 340, 341, 342, 343, 344, 345, 346]
2025-08-17 22:06:03,320 - 关键帧分析器 - INFO - API响应验证通过，包含10个有效的observation
2025-08-17 22:06:03,321 - 关键帧分析器 - INFO - 第3次尝试成功，获得有效的分析结果
2025-08-17 22:06:03,321 - 关键帧分析器 - INFO - 已更新scene_id 317 的observation
2025-08-17 22:06:03,321 - 关键帧分析器 - INFO - 已更新scene_id 318 的observation
2025-08-17 22:06:03,322 - 关键帧分析器 - INFO - 已更新scene_id 319 的observation
2025-08-17 22:06:03,322 - 关键帧分析器 - INFO - 已更新scene_id 320 的observation
2025-08-17 22:06:03,323 - 关键帧分析器 - INFO - 已更新scene_id 321 的observation
2025-08-17 22:06:03,323 - 关键帧分析器 - INFO - 已更新scene_id 322 的observation
2025-08-17 22:06:03,323 - 关键帧分析器 - INFO - 已更新scene_id 323 的observation
2025-08-17 22:06:03,323 - 关键帧分析器 - INFO - 已更新scene_id 324 的observation
2025-08-17 22:06:03,323 - 关键帧分析器 - INFO - 已更新scene_id 325 的observation
2025-08-17 22:06:03,323 - 关键帧分析器 - INFO - 已更新scene_id 326 的observation
2025-08-17 22:06:03,323 - 关键帧分析器 - INFO - 成功更新 10 个场景的observation
2025-08-17 22:06:03,323 - 关键帧分析器 - INFO - 第 30 批处理成功
2025-08-17 22:06:03,324 - 关键帧分析器 - INFO - 线程处理第 35 批: scene [367, 368, 369, 370, 371, 372, 373, 374, 375, 376]
2025-08-17 22:06:03,363 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [367, 368, 369, 370, 371, 372, 373, 374, 375, 376]
2025-08-17 22:06:07,006 - 关键帧分析器 - ERROR - 第3次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:06:07,007 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [347, 348, 349, 350, 351, 352, 353, 354, 355, 356] 分析失败
2025-08-17 22:06:07,007 - 关键帧分析器 - ERROR - 第 33 批API分析失败
2025-08-17 22:06:07,007 - 关键帧分析器 - INFO - 线程处理第 36 批: scene [377, 378, 379, 380, 381, 382, 383, 384, 385, 386]
2025-08-17 22:06:07,060 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [377, 378, 379, 380, 381, 382, 383, 384, 385, 386]
2025-08-17 22:06:13,869 - 关键帧分析器 - ERROR - 第3次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:06:13,869 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [327, 328, 329, 330, 331, 332, 333, 334, 335, 336] 分析失败
2025-08-17 22:06:13,869 - 关键帧分析器 - ERROR - 第 31 批API分析失败
2025-08-17 22:06:13,870 - 关键帧分析器 - INFO - 线程处理第 37 批: scene [387, 388, 389, 390, 391, 392, 393, 394, 395, 396]
2025-08-17 22:06:13,908 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [387, 388, 389, 390, 391, 392, 393, 394, 395, 396]
2025-08-17 22:06:16,076 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:06:16,077 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [357, 358, 359, 360, 361, 362, 363, 364, 365, 366]
2025-08-17 22:06:23,197 - 关键帧分析器 - ERROR - 第3次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:06:23,197 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [337, 338, 339, 340, 341, 342, 343, 344, 345, 346] 分析失败
2025-08-17 22:06:23,197 - 关键帧分析器 - ERROR - 第 32 批API分析失败
2025-08-17 22:06:23,197 - 关键帧分析器 - INFO - 线程处理第 38 批: scene [397, 398, 399, 400, 401, 402, 403, 404, 405, 406]
2025-08-17 22:06:23,228 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [397, 398, 399, 400, 401, 402, 403, 404, 405, 406]
2025-08-17 22:06:23,441 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:06:23,441 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [367, 368, 369, 370, 371, 372, 373, 374, 375, 376]
2025-08-17 22:06:25,479 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:06:25,479 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [377, 378, 379, 380, 381, 382, 383, 384, 385, 386]
2025-08-17 22:06:34,612 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:06:34,612 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [387, 388, 389, 390, 391, 392, 393, 394, 395, 396]
2025-08-17 22:06:39,002 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:06:39,002 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [397, 398, 399, 400, 401, 402, 403, 404, 405, 406]
2025-08-17 22:06:43,172 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:06:43,173 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [377, 378, 379, 380, 381, 382, 383, 384, 385, 386]
2025-08-17 22:06:43,274 - 关键帧分析器 - ERROR - 第3次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:06:43,274 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [357, 358, 359, 360, 361, 362, 363, 364, 365, 366] 分析失败
2025-08-17 22:06:43,274 - 关键帧分析器 - ERROR - 第 34 批API分析失败
2025-08-17 22:06:43,275 - 关键帧分析器 - INFO - 线程处理第 39 批: scene [407, 408, 409, 410, 411, 412, 413, 414, 415, 416]
2025-08-17 22:06:43,305 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [407, 408, 409, 410, 411, 412, 413, 414, 415, 416]
2025-08-17 22:06:48,046 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:06:48,046 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [367, 368, 369, 370, 371, 372, 373, 374, 375, 376]
2025-08-17 22:06:53,594 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:06:53,594 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [387, 388, 389, 390, 391, 392, 393, 394, 395, 396]
2025-08-17 22:06:55,800 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:06:55,800 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [397, 398, 399, 400, 401, 402, 403, 404, 405, 406]
2025-08-17 22:07:06,839 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:07:06,839 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [407, 408, 409, 410, 411, 412, 413, 414, 415, 416]
2025-08-17 22:07:08,919 - 关键帧分析器 - ERROR - 第3次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:07:08,920 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [377, 378, 379, 380, 381, 382, 383, 384, 385, 386] 分析失败
2025-08-17 22:07:08,920 - 关键帧分析器 - ERROR - 第 36 批API分析失败
2025-08-17 22:07:08,920 - 关键帧分析器 - INFO - 线程处理第 40 批: scene [417, 418, 419, 420, 421, 422, 423, 424, 425, 426]
2025-08-17 22:07:08,948 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [417, 418, 419, 420, 421, 422, 423, 424, 425, 426]
2025-08-17 22:07:11,098 - 关键帧分析器 - ERROR - 第3次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:07:11,098 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [367, 368, 369, 370, 371, 372, 373, 374, 375, 376] 分析失败
2025-08-17 22:07:11,098 - 关键帧分析器 - ERROR - 第 35 批API分析失败
2025-08-17 22:07:11,099 - 关键帧分析器 - INFO - 线程处理第 41 批: scene [427, 428, 429, 430, 431, 432, 433, 434, 435, 436]
2025-08-17 22:07:11,128 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [427, 428, 429, 430, 431, 432, 433, 434, 435, 436]
2025-08-17 22:16:00,287 - 关键帧分析器 - INFO - ============================================================
2025-08-17 22:16:00,287 - 关键帧分析器 - INFO - 视频关键帧分析工具启动
2025-08-17 22:16:00,287 - 关键帧分析器 - INFO - 每批处理图片数量: 10
2025-08-17 22:16:00,287 - 关键帧分析器 - INFO - 最大重试次数: 3
2025-08-17 22:16:00,288 - 关键帧分析器 - INFO - 并行线程数: 2
2025-08-17 22:16:00,288 - 关键帧分析器 - INFO - API超时时间: 600秒
2025-08-17 22:16:00,288 - 关键帧分析器 - INFO - 请求延迟: 1秒
2025-08-17 22:16:00,288 - 关键帧分析器 - INFO - ============================================================
2025-08-17 22:16:00,288 - 关键帧分析器 - INFO - 检查scenes_report.json文件...
2025-08-17 22:16:00,329 - 关键帧分析器 - INFO - 成功加载scenes_report.json，包含 3221 个场景
2025-08-17 22:16:00,329 - 关键帧分析器 - INFO - 开始扫描目录: F:\github\aicut_auto\ai-video-splitter\Keyframes
2025-08-17 22:16:00,335 - 关键帧分析器 - INFO - 找到 3221 个关键帧图片文件
2025-08-17 22:16:00,335 - 关键帧分析器 - INFO - 开始按scene编号排序图片文件
2025-08-17 22:16:00,342 - 关键帧分析器 - INFO - 成功排序 3221 个图片文件
2025-08-17 22:16:00,342 - 关键帧分析器 - INFO - Scene编号范围: 1 - 3221
2025-08-17 22:16:00,342 - 关键帧分析器 - INFO - 检查scenes_report.json中已有的observation...
2025-08-17 22:16:00,344 - 关键帧分析器 - INFO - 总共 3221 个scene
2025-08-17 22:16:00,344 - 关键帧分析器 - INFO - 已有observation的scene: 26 个
2025-08-17 22:16:00,344 - 关键帧分析器 - INFO - 需要处理的scene: 3195 个
2025-08-17 22:16:00,344 - 关键帧分析器 - INFO - 跳过的scene_id: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]...
2025-08-17 22:16:00,344 - 关键帧分析器 - INFO - 准备处理 3195 张图片
2025-08-17 22:16:00,344 - 关键帧分析器 - INFO - 开始并行分批处理 3195 张图片，共 320 批，每批 10 张
2025-08-17 22:16:00,344 - 关键帧分析器 - INFO - 使用 2 个并行线程
2025-08-17 22:16:00,349 - 关键帧分析器 - INFO - 线程处理第 1 批: scene [15, 16, 29, 30, 31, 32, 33, 34, 35, 36]
2025-08-17 22:16:00,349 - 关键帧分析器 - INFO - 线程处理第 2 批: scene [37, 38, 39, 40, 41, 42, 43, 44, 45, 46]
2025-08-17 22:16:00,387 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [37, 38, 39, 40, 41, 42, 43, 44, 45, 46]
2025-08-17 22:16:00,391 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [15, 16, 29, 30, 31, 32, 33, 34, 35, 36]
2025-08-17 22:16:00,504 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 401 Client Error: Unauthorized for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:16:00,537 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 401 Client Error: Unauthorized for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:16:02,505 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [37, 38, 39, 40, 41, 42, 43, 44, 45, 46]
2025-08-17 22:16:02,538 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [15, 16, 29, 30, 31, 32, 33, 34, 35, 36]
2025-08-17 22:16:02,642 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 401 Client Error: Unauthorized for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:16:02,656 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 401 Client Error: Unauthorized for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:16:05,643 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [37, 38, 39, 40, 41, 42, 43, 44, 45, 46]
2025-08-17 22:16:05,666 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [15, 16, 29, 30, 31, 32, 33, 34, 35, 36]
2025-08-17 22:16:05,731 - 关键帧分析器 - ERROR - 第3次尝试: API请求失败: 401 Client Error: Unauthorized for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:16:05,731 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [37, 38, 39, 40, 41, 42, 43, 44, 45, 46] 分析失败
2025-08-17 22:16:05,731 - 关键帧分析器 - ERROR - 第 2 批API分析失败
2025-08-17 22:16:05,731 - 关键帧分析器 - INFO - 线程处理第 3 批: scene [47, 48, 49, 50, 51, 52, 53, 54, 55, 56]
2025-08-17 22:16:05,749 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [47, 48, 49, 50, 51, 52, 53, 54, 55, 56]
2025-08-17 22:16:05,774 - 关键帧分析器 - ERROR - 第3次尝试: API请求失败: 401 Client Error: Unauthorized for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:16:05,775 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [15, 16, 29, 30, 31, 32, 33, 34, 35, 36] 分析失败
2025-08-17 22:16:05,775 - 关键帧分析器 - ERROR - 第 1 批API分析失败
2025-08-17 22:16:05,775 - 关键帧分析器 - INFO - 线程处理第 4 批: scene [57, 58, 59, 60, 61, 62, 63, 64, 65, 66]
2025-08-17 22:16:05,789 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [57, 58, 59, 60, 61, 62, 63, 64, 65, 66]
2025-08-17 22:16:05,820 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 401 Client Error: Unauthorized for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:16:05,864 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 401 Client Error: Unauthorized for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:20:10,057 - 关键帧分析器 - INFO - ============================================================
2025-08-17 22:20:10,058 - 关键帧分析器 - INFO - 视频关键帧分析工具启动
2025-08-17 22:20:10,058 - 关键帧分析器 - INFO - 每批处理图片数量: 10
2025-08-17 22:20:10,058 - 关键帧分析器 - INFO - 最大重试次数: 3
2025-08-17 22:20:10,058 - 关键帧分析器 - INFO - 并行线程数: 2
2025-08-17 22:20:10,058 - 关键帧分析器 - INFO - API超时时间: 600秒
2025-08-17 22:20:10,058 - 关键帧分析器 - INFO - 请求延迟: 1秒
2025-08-17 22:20:10,058 - 关键帧分析器 - INFO - ============================================================
2025-08-17 22:20:10,058 - 关键帧分析器 - INFO - 检查scenes_report.json文件...
2025-08-17 22:20:10,100 - 关键帧分析器 - INFO - 成功加载scenes_report.json，包含 3221 个场景
2025-08-17 22:20:10,100 - 关键帧分析器 - INFO - 开始扫描目录: F:\github\aicut_auto\ai-video-splitter\Keyframes
2025-08-17 22:20:10,105 - 关键帧分析器 - INFO - 找到 3221 个关键帧图片文件
2025-08-17 22:20:10,105 - 关键帧分析器 - INFO - 开始按scene编号排序图片文件
2025-08-17 22:20:10,112 - 关键帧分析器 - INFO - 成功排序 3221 个图片文件
2025-08-17 22:20:10,113 - 关键帧分析器 - INFO - Scene编号范围: 1 - 3221
2025-08-17 22:20:10,113 - 关键帧分析器 - INFO - 检查scenes_report.json中已有的observation...
2025-08-17 22:20:10,119 - 关键帧分析器 - INFO - 总共 3221 个scene
2025-08-17 22:20:10,119 - 关键帧分析器 - INFO - 已有observation的scene: 26 个
2025-08-17 22:20:10,119 - 关键帧分析器 - INFO - 需要处理的scene: 3195 个
2025-08-17 22:20:10,119 - 关键帧分析器 - INFO - 跳过的scene_id: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]...
2025-08-17 22:20:10,119 - 关键帧分析器 - INFO - 限制处理图片数量为: 5
2025-08-17 22:20:10,119 - 关键帧分析器 - INFO - 准备处理 5 张图片
2025-08-17 22:20:10,120 - 关键帧分析器 - INFO - 开始并行分批处理 5 张图片，共 1 批，每批 10 张
2025-08-17 22:20:10,120 - 关键帧分析器 - INFO - 使用 2 个并行线程
2025-08-17 22:20:10,120 - 关键帧分析器 - INFO - 线程处理第 1 批: scene [15, 16, 29, 30, 31]
2025-08-17 22:20:10,129 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [15, 16, 29, 30, 31]
2025-08-17 22:20:17,932 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:20:19,933 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [15, 16, 29, 30, 31]
2025-08-17 22:20:25,406 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:20:28,407 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [15, 16, 29, 30, 31]
2025-08-17 22:20:31,523 - 关键帧分析器 - ERROR - 第3次尝试: API返回的choices数组为空
2025-08-17 22:20:31,524 - 关键帧分析器 - ERROR - 完整API响应: {'id': 'chatcmpl-665471c6-d3e4-4228-b3a1-739ed476240d', 'object': 'chat.completion', 'created': 1755440431, 'model': 'gemini-2.5-flash-preview-05-20', 'choices': [], 'usage': {'prompt_tokens': 1538, 'completion_tokens': 0, 'total_tokens': 1538}}
2025-08-17 22:20:31,524 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [15, 16, 29, 30, 31] 分析失败
2025-08-17 22:20:31,524 - 关键帧分析器 - ERROR - 第 1 批API分析失败
2025-08-17 22:20:31,524 - 关键帧分析器 - INFO - 
并行处理完成: 成功 0/1 批
2025-08-17 22:20:31,524 - 关键帧分析器 - ERROR - 失败的scene_id: [15, 16, 29, 30, 31]
2025-08-17 22:20:31,524 - 关键帧分析器 - INFO - 
============================================================
2025-08-17 22:20:31,525 - 关键帧分析器 - INFO - 处理完成报告
2025-08-17 22:20:31,525 - 关键帧分析器 - INFO - ============================================================
2025-08-17 22:20:31,525 - 关键帧分析器 - INFO - 总图片数量: 3221
2025-08-17 22:20:31,525 - 关键帧分析器 - INFO - 需要处理的图片数量: 5
2025-08-17 22:20:31,525 - 关键帧分析器 - INFO - 成功处理批次: 0
2025-08-17 22:20:31,525 - 关键帧分析器 - ERROR - 失败的scene_id数量: 5
2025-08-17 22:20:31,525 - 关键帧分析器 - ERROR - 失败的scene_id列表: [15, 16, 29, 30, 31]
2025-08-17 22:20:31,525 - 关键帧分析器 - ERROR - 建议检查:
2025-08-17 22:20:31,525 - 关键帧分析器 - ERROR - 1. 网络连接是否正常
2025-08-17 22:20:31,525 - 关键帧分析器 - ERROR - 2. API服务是否可用
2025-08-17 22:20:31,525 - 关键帧分析器 - ERROR - 3. 图片文件是否损坏
2025-08-17 22:20:31,525 - 关键帧分析器 - INFO - ============================================================
2025-08-17 22:21:34,072 - 关键帧分析器 - INFO - ============================================================
2025-08-17 22:21:34,072 - 关键帧分析器 - INFO - 视频关键帧分析工具启动
2025-08-17 22:21:34,072 - 关键帧分析器 - INFO - 每批处理图片数量: 3
2025-08-17 22:21:34,072 - 关键帧分析器 - INFO - 最大重试次数: 3
2025-08-17 22:21:34,072 - 关键帧分析器 - INFO - 并行线程数: 2
2025-08-17 22:21:34,072 - 关键帧分析器 - INFO - API超时时间: 600秒
2025-08-17 22:21:34,072 - 关键帧分析器 - INFO - 请求延迟: 1秒
2025-08-17 22:21:34,072 - 关键帧分析器 - INFO - ============================================================
2025-08-17 22:21:34,072 - 关键帧分析器 - INFO - 检查scenes_report.json文件...
2025-08-17 22:21:34,114 - 关键帧分析器 - INFO - 成功加载scenes_report.json，包含 3221 个场景
2025-08-17 22:21:34,114 - 关键帧分析器 - INFO - 开始扫描目录: F:\github\aicut_auto\ai-video-splitter\Keyframes
2025-08-17 22:21:34,120 - 关键帧分析器 - INFO - 找到 3221 个关键帧图片文件
2025-08-17 22:21:34,120 - 关键帧分析器 - INFO - 开始按scene编号排序图片文件
2025-08-17 22:21:34,126 - 关键帧分析器 - INFO - 成功排序 3221 个图片文件
2025-08-17 22:21:34,126 - 关键帧分析器 - INFO - Scene编号范围: 1 - 3221
2025-08-17 22:21:34,126 - 关键帧分析器 - INFO - 检查scenes_report.json中已有的observation...
2025-08-17 22:21:34,130 - 关键帧分析器 - INFO - 总共 3221 个scene
2025-08-17 22:21:34,130 - 关键帧分析器 - INFO - 已有observation的scene: 26 个
2025-08-17 22:21:34,131 - 关键帧分析器 - INFO - 需要处理的scene: 3195 个
2025-08-17 22:21:34,131 - 关键帧分析器 - INFO - 跳过的scene_id: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]...
2025-08-17 22:21:34,131 - 关键帧分析器 - INFO - 限制处理图片数量为: 6
2025-08-17 22:21:34,131 - 关键帧分析器 - INFO - 准备处理 6 张图片
2025-08-17 22:21:34,131 - 关键帧分析器 - INFO - 开始并行分批处理 6 张图片，共 2 批，每批 3 张
2025-08-17 22:21:34,131 - 关键帧分析器 - INFO - 使用 2 个并行线程
2025-08-17 22:21:34,132 - 关键帧分析器 - INFO - 线程处理第 1 批: scene [15, 16, 29]
2025-08-17 22:21:34,132 - 关键帧分析器 - INFO - 线程处理第 2 批: scene [30, 31, 32]
2025-08-17 22:21:34,141 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [30, 31, 32]
2025-08-17 22:21:34,143 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [15, 16, 29]
2025-08-17 22:21:37,572 - 关键帧分析器 - ERROR - 第1次尝试: API返回的choices数组为空
2025-08-17 22:21:37,572 - 关键帧分析器 - ERROR - 完整API响应: {'id': 'chatcmpl-2a442d20-8c87-4c0f-a9ed-1ef9478d9ffc', 'object': 'chat.completion', 'created': 1755440497, 'model': 'gemini-2.5-flash-preview-05-20', 'choices': [], 'usage': {'prompt_tokens': 1022, 'completion_tokens': 0, 'total_tokens': 1022}}
2025-08-17 22:21:39,573 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [15, 16, 29]
2025-08-17 22:21:42,032 - 关键帧分析器 - ERROR - 第2次尝试: API返回的choices数组为空
2025-08-17 22:21:42,032 - 关键帧分析器 - ERROR - 完整API响应: {'id': 'chatcmpl-7a928f77-6193-4c01-b0c6-5bfc4aa7d49a', 'object': 'chat.completion', 'created': 1755440502, 'model': 'gemini-2.5-flash-preview-05-20', 'choices': [], 'usage': {'prompt_tokens': 1022, 'completion_tokens': 0, 'total_tokens': 1022}}
2025-08-17 22:21:44,085 - 关键帧分析器 - INFO - API响应验证通过，包含3个有效的observation
2025-08-17 22:21:44,085 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 22:21:44,086 - 关键帧分析器 - INFO - 已更新scene_id 30 的observation
2025-08-17 22:21:44,086 - 关键帧分析器 - INFO - 已更新scene_id 31 的observation
2025-08-17 22:21:44,086 - 关键帧分析器 - INFO - 已更新scene_id 32 的observation
2025-08-17 22:21:44,086 - 关键帧分析器 - INFO - 成功更新 3 个场景的observation
2025-08-17 22:21:44,086 - 关键帧分析器 - INFO - 第 2 批处理成功
2025-08-17 22:21:45,033 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [15, 16, 29]
2025-08-17 22:21:49,487 - 关键帧分析器 - ERROR - 第3次尝试: API返回的choices数组为空
2025-08-17 22:21:49,487 - 关键帧分析器 - ERROR - 完整API响应: {'id': 'chatcmpl-94877b95-ac1e-4bfb-a1a6-9113d874c81a', 'object': 'chat.completion', 'created': 1755440509, 'model': 'gemini-2.5-flash-preview-05-20', 'choices': [], 'usage': {'prompt_tokens': 1022, 'completion_tokens': 0, 'total_tokens': 1022}}
2025-08-17 22:21:49,487 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [15, 16, 29] 分析失败
2025-08-17 22:21:49,488 - 关键帧分析器 - ERROR - 第 1 批API分析失败
2025-08-17 22:21:49,488 - 关键帧分析器 - INFO - 
并行处理完成: 成功 1/2 批
2025-08-17 22:21:49,488 - 关键帧分析器 - ERROR - 失败的scene_id: [15, 16, 29]
2025-08-17 22:21:49,649 - 关键帧分析器 - INFO - 成功保存scenes_report.json: F:\github\aicut_auto\ai-video-splitter\scenes_report.json
2025-08-17 22:21:49,649 - 关键帧分析器 - INFO - scenes_report.json已成功更新
2025-08-17 22:21:49,649 - 关键帧分析器 - INFO - 
============================================================
2025-08-17 22:21:49,650 - 关键帧分析器 - INFO - 处理完成报告
2025-08-17 22:21:49,650 - 关键帧分析器 - INFO - ============================================================
2025-08-17 22:21:49,650 - 关键帧分析器 - INFO - 总图片数量: 3221
2025-08-17 22:21:49,650 - 关键帧分析器 - INFO - 需要处理的图片数量: 6
2025-08-17 22:21:49,650 - 关键帧分析器 - INFO - 成功处理批次: 1
2025-08-17 22:21:49,650 - 关键帧分析器 - ERROR - 失败的scene_id数量: 3
2025-08-17 22:21:49,650 - 关键帧分析器 - ERROR - 失败的scene_id列表: [15, 16, 29]
2025-08-17 22:21:49,650 - 关键帧分析器 - ERROR - 建议检查:
2025-08-17 22:21:49,650 - 关键帧分析器 - ERROR - 1. 网络连接是否正常
2025-08-17 22:21:49,650 - 关键帧分析器 - ERROR - 2. API服务是否可用
2025-08-17 22:21:49,650 - 关键帧分析器 - ERROR - 3. 图片文件是否损坏
2025-08-17 22:21:49,650 - 关键帧分析器 - INFO - ============================================================
2025-08-17 22:22:56,997 - 关键帧分析器 - INFO - ============================================================
2025-08-17 22:22:56,997 - 关键帧分析器 - INFO - 视频关键帧分析工具启动
2025-08-17 22:22:56,998 - 关键帧分析器 - INFO - 每批处理图片数量: 3
2025-08-17 22:22:56,998 - 关键帧分析器 - INFO - 最大重试次数: 3
2025-08-17 22:22:56,998 - 关键帧分析器 - INFO - 并行线程数: 5
2025-08-17 22:22:56,998 - 关键帧分析器 - INFO - API超时时间: 600秒
2025-08-17 22:22:56,998 - 关键帧分析器 - INFO - 请求延迟: 1秒
2025-08-17 22:22:56,998 - 关键帧分析器 - INFO - ============================================================
2025-08-17 22:22:56,998 - 关键帧分析器 - INFO - 检查scenes_report.json文件...
2025-08-17 22:22:57,039 - 关键帧分析器 - INFO - 成功加载scenes_report.json，包含 3221 个场景
2025-08-17 22:22:57,039 - 关键帧分析器 - INFO - 开始扫描目录: F:\github\aicut_auto\ai-video-splitter\Keyframes
2025-08-17 22:22:57,045 - 关键帧分析器 - INFO - 找到 3221 个关键帧图片文件
2025-08-17 22:22:57,045 - 关键帧分析器 - INFO - 开始按scene编号排序图片文件
2025-08-17 22:22:57,051 - 关键帧分析器 - INFO - 成功排序 3221 个图片文件
2025-08-17 22:22:57,051 - 关键帧分析器 - INFO - Scene编号范围: 1 - 3221
2025-08-17 22:22:57,051 - 关键帧分析器 - INFO - 检查scenes_report.json中已有的observation...
2025-08-17 22:22:57,052 - 关键帧分析器 - INFO - 总共 3221 个scene
2025-08-17 22:22:57,053 - 关键帧分析器 - INFO - 已有observation的scene: 29 个
2025-08-17 22:22:57,053 - 关键帧分析器 - INFO - 需要处理的scene: 3192 个
2025-08-17 22:22:57,053 - 关键帧分析器 - INFO - 跳过的scene_id: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]...
2025-08-17 22:22:57,053 - 关键帧分析器 - INFO - 准备处理 3192 张图片
2025-08-17 22:22:57,053 - 关键帧分析器 - INFO - 开始并行分批处理 3192 张图片，共 1064 批，每批 3 张
2025-08-17 22:22:57,053 - 关键帧分析器 - INFO - 使用 5 个并行线程
2025-08-17 22:22:57,057 - 关键帧分析器 - INFO - 线程处理第 1 批: scene [15, 16, 29]
2025-08-17 22:22:57,057 - 关键帧分析器 - INFO - 线程处理第 2 批: scene [33, 34, 35]
2025-08-17 22:22:57,057 - 关键帧分析器 - INFO - 线程处理第 3 批: scene [36, 37, 38]
2025-08-17 22:22:57,057 - 关键帧分析器 - INFO - 线程处理第 4 批: scene [39, 40, 41]
2025-08-17 22:22:57,058 - 关键帧分析器 - INFO - 线程处理第 5 批: scene [42, 43, 44]
2025-08-17 22:22:57,082 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [15, 16, 29]
2025-08-17 22:22:57,086 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [33, 34, 35]
2025-08-17 22:22:57,086 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [36, 37, 38]
2025-08-17 22:22:57,094 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [39, 40, 41]
2025-08-17 22:22:57,094 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [42, 43, 44]
2025-08-17 22:23:02,660 - 关键帧分析器 - ERROR - 第1次尝试: API返回的choices数组为空
2025-08-17 22:23:02,660 - 关键帧分析器 - ERROR - 完整API响应: {'id': 'chatcmpl-35d2a63d-1254-4d50-b334-6403753cfd57', 'object': 'chat.completion', 'created': 1755440582, 'model': 'gemini-2.5-flash-preview-05-20', 'choices': [], 'usage': {'prompt_tokens': 1022, 'completion_tokens': 0, 'total_tokens': 1022}}
2025-08-17 22:23:03,155 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:23:03,589 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:23:04,661 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [15, 16, 29]
2025-08-17 22:23:05,156 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [33, 34, 35]
2025-08-17 22:23:05,590 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [39, 40, 41]
2025-08-17 22:23:07,439 - 关键帧分析器 - INFO - API响应验证通过，包含3个有效的observation
2025-08-17 22:23:07,439 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 22:23:07,439 - 关键帧分析器 - INFO - 已更新scene_id 36 的observation
2025-08-17 22:23:07,439 - 关键帧分析器 - INFO - 已更新scene_id 37 的observation
2025-08-17 22:23:07,439 - 关键帧分析器 - INFO - 已更新scene_id 38 的observation
2025-08-17 22:23:07,439 - 关键帧分析器 - INFO - 成功更新 3 个场景的observation
2025-08-17 22:23:07,440 - 关键帧分析器 - INFO - 第 3 批处理成功
2025-08-17 22:23:07,440 - 关键帧分析器 - INFO - 线程处理第 6 批: scene [45, 46, 47]
2025-08-17 22:23:07,446 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [45, 46, 47]
2025-08-17 22:23:08,063 - 关键帧分析器 - ERROR - 第2次尝试: API返回的choices数组为空
2025-08-17 22:23:08,064 - 关键帧分析器 - ERROR - 完整API响应: {'id': 'chatcmpl-94061183-7b70-49b2-b763-a0ff7494cfe9', 'object': 'chat.completion', 'created': 1755440588, 'model': 'gemini-2.5-flash-preview-05-20', 'choices': [], 'usage': {'prompt_tokens': 1022, 'completion_tokens': 0, 'total_tokens': 1022}}
2025-08-17 22:23:08,152 - 关键帧分析器 - INFO - API响应验证通过，包含3个有效的observation
2025-08-17 22:23:08,153 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 22:23:08,153 - 关键帧分析器 - INFO - 已更新scene_id 42 的observation
2025-08-17 22:23:08,153 - 关键帧分析器 - INFO - 已更新scene_id 43 的observation
2025-08-17 22:23:08,153 - 关键帧分析器 - INFO - 已更新scene_id 44 的observation
2025-08-17 22:23:08,153 - 关键帧分析器 - INFO - 成功更新 3 个场景的observation
2025-08-17 22:23:08,153 - 关键帧分析器 - INFO - 第 5 批处理成功
2025-08-17 22:23:08,153 - 关键帧分析器 - INFO - 线程处理第 7 批: scene [48, 49, 50]
2025-08-17 22:23:08,157 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [48, 49, 50]
2025-08-17 22:23:11,064 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [15, 16, 29]
2025-08-17 22:23:13,357 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:23:15,358 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [48, 49, 50]
2025-08-17 22:23:17,588 - 关键帧分析器 - ERROR - 第3次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:23:17,588 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [15, 16, 29] 分析失败
2025-08-17 22:23:17,588 - 关键帧分析器 - ERROR - 第 1 批API分析失败
2025-08-17 22:23:17,588 - 关键帧分析器 - INFO - 线程处理第 8 批: scene [51, 52, 53]
2025-08-17 22:23:17,594 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [51, 52, 53]
2025-08-17 22:23:17,923 - 关键帧分析器 - INFO - API响应验证通过，包含3个有效的observation
2025-08-17 22:23:17,923 - 关键帧分析器 - INFO - 第2次尝试成功，获得有效的分析结果
2025-08-17 22:23:17,924 - 关键帧分析器 - INFO - 已更新scene_id 39 的observation
2025-08-17 22:23:17,924 - 关键帧分析器 - INFO - 已更新scene_id 40 的observation
2025-08-17 22:23:17,924 - 关键帧分析器 - INFO - 已更新scene_id 41 的observation
2025-08-17 22:23:17,924 - 关键帧分析器 - INFO - 成功更新 3 个场景的observation
2025-08-17 22:23:17,924 - 关键帧分析器 - INFO - 第 4 批处理成功
2025-08-17 22:23:17,924 - 关键帧分析器 - INFO - 线程处理第 9 批: scene [54, 55, 56]
2025-08-17 22:23:17,931 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [54, 55, 56]
2025-08-17 22:23:20,831 - 关键帧分析器 - INFO - API响应验证通过，包含3个有效的observation
2025-08-17 22:23:20,833 - 关键帧分析器 - INFO - 第2次尝试成功，获得有效的分析结果
2025-08-17 22:23:20,833 - 关键帧分析器 - INFO - 已更新scene_id 33 的observation
2025-08-17 22:23:20,833 - 关键帧分析器 - INFO - 已更新scene_id 34 的observation
2025-08-17 22:23:20,833 - 关键帧分析器 - INFO - 已更新scene_id 35 的observation
2025-08-17 22:23:20,833 - 关键帧分析器 - INFO - 成功更新 3 个场景的observation
2025-08-17 22:23:20,833 - 关键帧分析器 - INFO - 第 2 批处理成功
2025-08-17 22:23:20,833 - 关键帧分析器 - INFO - 线程处理第 10 批: scene [57, 58, 59]
2025-08-17 22:23:20,838 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [57, 58, 59]
2025-08-17 22:23:21,141 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:23:24,142 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [48, 49, 50]
2025-08-17 22:23:25,052 - 关键帧分析器 - INFO - API响应验证通过，包含3个有效的observation
2025-08-17 22:23:25,052 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 22:23:25,052 - 关键帧分析器 - INFO - 已更新scene_id 45 的observation
2025-08-17 22:23:25,052 - 关键帧分析器 - INFO - 已更新scene_id 46 的observation
2025-08-17 22:23:25,052 - 关键帧分析器 - INFO - 已更新scene_id 47 的observation
2025-08-17 22:23:25,052 - 关键帧分析器 - INFO - 成功更新 3 个场景的observation
2025-08-17 22:23:25,052 - 关键帧分析器 - INFO - 第 6 批处理成功
2025-08-17 22:23:25,052 - 关键帧分析器 - INFO - 线程处理第 11 批: scene [60, 61, 62]
2025-08-17 22:23:25,056 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [60, 61, 62]
2025-08-17 22:23:25,240 - 关键帧分析器 - ERROR - 第1次尝试: API返回的choices数组为空
2025-08-17 22:23:25,241 - 关键帧分析器 - ERROR - 完整API响应: {'id': 'chatcmpl-fef207d1-1749-4e28-89bf-64477fdaa174', 'object': 'chat.completion', 'created': 1755440605, 'model': 'gemini-2.5-flash-preview-05-20', 'choices': [], 'usage': {'prompt_tokens': 1022, 'completion_tokens': 0, 'total_tokens': 1022}}
2025-08-17 22:23:27,242 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [51, 52, 53]
2025-08-17 22:23:28,754 - 关键帧分析器 - INFO - API响应验证通过，包含3个有效的observation
2025-08-17 22:23:28,754 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 22:23:28,754 - 关键帧分析器 - INFO - 已更新scene_id 54 的observation
2025-08-17 22:23:28,754 - 关键帧分析器 - INFO - 已更新scene_id 55 的observation
2025-08-17 22:23:28,754 - 关键帧分析器 - INFO - 已更新scene_id 56 的observation
2025-08-17 22:23:28,754 - 关键帧分析器 - INFO - 成功更新 3 个场景的observation
2025-08-17 22:23:28,755 - 关键帧分析器 - INFO - 第 9 批处理成功
2025-08-17 22:23:28,755 - 关键帧分析器 - INFO - 线程处理第 12 批: scene [63, 64, 65]
2025-08-17 22:23:28,760 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [63, 64, 65]
2025-08-17 22:23:30,428 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:23:32,430 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [60, 61, 62]
2025-08-17 22:23:34,222 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:23:34,833 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:23:35,694 - 关键帧分析器 - INFO - API响应验证通过，包含3个有效的observation
2025-08-17 22:23:35,694 - 关键帧分析器 - INFO - 第3次尝试成功，获得有效的分析结果
2025-08-17 22:23:35,695 - 关键帧分析器 - INFO - 已更新scene_id 48 的observation
2025-08-17 22:23:35,695 - 关键帧分析器 - INFO - 已更新scene_id 49 的observation
2025-08-17 22:23:35,695 - 关键帧分析器 - INFO - 已更新scene_id 50 的observation
2025-08-17 22:23:35,695 - 关键帧分析器 - INFO - 成功更新 3 个场景的observation
2025-08-17 22:23:35,695 - 关键帧分析器 - INFO - 第 7 批处理成功
2025-08-17 22:23:35,695 - 关键帧分析器 - INFO - 线程处理第 13 批: scene [66, 67, 68]
2025-08-17 22:23:35,699 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [66, 67, 68]
2025-08-17 22:23:36,834 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [63, 64, 65]
2025-08-17 22:23:37,224 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [51, 52, 53]
2025-08-17 22:23:37,402 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:23:40,407 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [60, 61, 62]
2025-08-17 22:23:42,040 - 关键帧分析器 - INFO - API响应验证通过，包含3个有效的observation
2025-08-17 22:23:42,041 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 22:23:42,041 - 关键帧分析器 - INFO - 已更新scene_id 57 的observation
2025-08-17 22:23:42,041 - 关键帧分析器 - INFO - 已更新scene_id 58 的observation
2025-08-17 22:23:42,041 - 关键帧分析器 - INFO - 已更新scene_id 59 的observation
2025-08-17 22:23:42,041 - 关键帧分析器 - INFO - 成功更新 3 个场景的observation
2025-08-17 22:23:42,041 - 关键帧分析器 - INFO - 第 10 批处理成功
2025-08-17 22:23:42,041 - 关键帧分析器 - INFO - 线程处理第 14 批: scene [69, 70, 71]
2025-08-17 22:23:42,045 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [69, 70, 71]
2025-08-17 22:23:42,761 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:23:44,762 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [66, 67, 68]
2025-08-17 22:23:44,919 - 关键帧分析器 - ERROR - 第3次尝试: API返回的choices数组为空
2025-08-17 22:23:44,920 - 关键帧分析器 - ERROR - 完整API响应: {'id': 'chatcmpl-c2e5a816-7c62-4601-8097-2f28ab406968', 'object': 'chat.completion', 'created': 1755440624, 'model': 'gemini-2.5-flash-preview-05-20', 'choices': [], 'usage': {'prompt_tokens': 1022, 'completion_tokens': 0, 'total_tokens': 1022}}
2025-08-17 22:23:44,920 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [51, 52, 53] 分析失败
2025-08-17 22:23:44,920 - 关键帧分析器 - ERROR - 第 8 批API分析失败
2025-08-17 22:23:44,920 - 关键帧分析器 - INFO - 线程处理第 15 批: scene [72, 73, 74]
2025-08-17 22:23:44,925 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [72, 73, 74]
2025-08-17 22:23:51,024 - 关键帧分析器 - INFO - API响应验证通过，包含3个有效的observation
2025-08-17 22:23:51,024 - 关键帧分析器 - INFO - 第2次尝试成功，获得有效的分析结果
2025-08-17 22:23:51,025 - 关键帧分析器 - INFO - 已更新scene_id 63 的observation
2025-08-17 22:23:51,025 - 关键帧分析器 - INFO - 已更新scene_id 64 的observation
2025-08-17 22:23:51,025 - 关键帧分析器 - INFO - 已更新scene_id 65 的observation
2025-08-17 22:23:51,025 - 关键帧分析器 - INFO - 成功更新 3 个场景的observation
2025-08-17 22:23:51,025 - 关键帧分析器 - INFO - 第 12 批处理成功
2025-08-17 22:23:51,025 - 关键帧分析器 - INFO - 线程处理第 16 批: scene [75, 76, 77]
2025-08-17 22:23:51,029 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [75, 76, 77]
2025-08-17 22:23:51,935 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:23:53,936 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [72, 73, 74]
2025-08-17 22:23:54,653 - 关键帧分析器 - INFO - API响应验证通过，包含3个有效的observation
2025-08-17 22:23:54,653 - 关键帧分析器 - INFO - 第3次尝试成功，获得有效的分析结果
2025-08-17 22:23:54,654 - 关键帧分析器 - INFO - 已更新scene_id 60 的observation
2025-08-17 22:23:54,654 - 关键帧分析器 - INFO - 已更新scene_id 61 的observation
2025-08-17 22:23:54,654 - 关键帧分析器 - INFO - 已更新scene_id 62 的observation
2025-08-17 22:23:54,654 - 关键帧分析器 - INFO - 成功更新 3 个场景的observation
2025-08-17 22:23:54,654 - 关键帧分析器 - INFO - 第 11 批处理成功
2025-08-17 22:23:54,654 - 关键帧分析器 - INFO - 线程处理第 17 批: scene [78, 79, 80]
2025-08-17 22:23:54,659 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [78, 79, 80]
2025-08-17 22:23:54,927 - 关键帧分析器 - INFO - API响应验证通过，包含3个有效的observation
2025-08-17 22:23:54,927 - 关键帧分析器 - INFO - 第2次尝试成功，获得有效的分析结果
2025-08-17 22:23:54,927 - 关键帧分析器 - INFO - 已更新scene_id 66 的observation
2025-08-17 22:23:54,927 - 关键帧分析器 - INFO - 已更新scene_id 67 的observation
2025-08-17 22:23:54,928 - 关键帧分析器 - INFO - 已更新scene_id 68 的observation
2025-08-17 22:23:54,928 - 关键帧分析器 - INFO - 成功更新 3 个场景的observation
2025-08-17 22:23:54,928 - 关键帧分析器 - INFO - 第 13 批处理成功
2025-08-17 22:23:54,928 - 关键帧分析器 - INFO - 线程处理第 18 批: scene [81, 82, 83]
2025-08-17 22:23:54,935 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [81, 82, 83]
2025-08-17 22:23:57,305 - 关键帧分析器 - INFO - API响应验证通过，包含3个有效的observation
2025-08-17 22:23:57,305 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 22:23:57,305 - 关键帧分析器 - INFO - 已更新scene_id 69 的observation
2025-08-17 22:23:57,306 - 关键帧分析器 - INFO - 已更新scene_id 70 的observation
2025-08-17 22:23:57,306 - 关键帧分析器 - INFO - 已更新scene_id 71 的observation
2025-08-17 22:23:57,306 - 关键帧分析器 - INFO - 成功更新 3 个场景的observation
2025-08-17 22:23:57,306 - 关键帧分析器 - INFO - 第 14 批处理成功
2025-08-17 22:23:57,306 - 关键帧分析器 - INFO - 线程处理第 19 批: scene [84, 85, 86]
2025-08-17 22:23:57,312 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [84, 85, 86]
2025-08-17 22:23:59,604 - 关键帧分析器 - ERROR - 第2次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:24:02,479 - 关键帧分析器 - ERROR - 第1次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:24:02,605 - 关键帧分析器 - INFO - 第3次尝试调用API分析scene [72, 73, 74]
2025-08-17 22:24:02,629 - 关键帧分析器 - INFO - API响应验证通过，包含3个有效的observation
2025-08-17 22:24:02,629 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 22:24:02,630 - 关键帧分析器 - INFO - 已更新scene_id 75 的observation
2025-08-17 22:24:02,630 - 关键帧分析器 - INFO - 已更新scene_id 76 的observation
2025-08-17 22:24:02,630 - 关键帧分析器 - INFO - 已更新scene_id 77 的observation
2025-08-17 22:24:02,630 - 关键帧分析器 - INFO - 成功更新 3 个场景的observation
2025-08-17 22:24:02,631 - 关键帧分析器 - INFO - 第 16 批处理成功
2025-08-17 22:24:02,631 - 关键帧分析器 - INFO - 线程处理第 20 批: scene [87, 88, 89]
2025-08-17 22:24:02,636 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [87, 88, 89]
2025-08-17 22:24:04,480 - 关键帧分析器 - INFO - 第2次尝试调用API分析scene [81, 82, 83]
2025-08-17 22:24:05,206 - 关键帧分析器 - INFO - API响应验证通过，包含3个有效的observation
2025-08-17 22:24:05,207 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 22:24:05,207 - 关键帧分析器 - INFO - 已更新scene_id 78 的observation
2025-08-17 22:24:05,207 - 关键帧分析器 - INFO - 已更新scene_id 79 的observation
2025-08-17 22:24:05,207 - 关键帧分析器 - INFO - 已更新scene_id 80 的observation
2025-08-17 22:24:05,207 - 关键帧分析器 - INFO - 成功更新 3 个场景的observation
2025-08-17 22:24:05,207 - 关键帧分析器 - INFO - 第 17 批处理成功
2025-08-17 22:24:05,208 - 关键帧分析器 - INFO - 线程处理第 21 批: scene [90, 91, 92]
2025-08-17 22:24:05,213 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [90, 91, 92]
2025-08-17 22:24:06,938 - 关键帧分析器 - INFO - API响应验证通过，包含3个有效的observation
2025-08-17 22:24:06,938 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 22:24:06,938 - 关键帧分析器 - INFO - 已更新scene_id 84 的observation
2025-08-17 22:24:06,938 - 关键帧分析器 - INFO - 已更新scene_id 85 的observation
2025-08-17 22:24:06,938 - 关键帧分析器 - INFO - 已更新scene_id 86 的observation
2025-08-17 22:24:06,938 - 关键帧分析器 - INFO - 成功更新 3 个场景的observation
2025-08-17 22:24:06,938 - 关键帧分析器 - INFO - 第 19 批处理成功
2025-08-17 22:24:06,938 - 关键帧分析器 - INFO - 线程处理第 22 批: scene [93, 94, 95]
2025-08-17 22:24:06,944 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [93, 94, 95]
2025-08-17 22:24:08,118 - 关键帧分析器 - ERROR - 第3次尝试: API请求失败: 500 Server Error: Internal Server Error for url: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 22:24:08,118 - 关键帧分析器 - ERROR - 所有3次尝试都失败，scene [72, 73, 74] 分析失败
2025-08-17 22:24:08,118 - 关键帧分析器 - ERROR - 第 15 批API分析失败
2025-08-17 22:24:08,118 - 关键帧分析器 - INFO - 线程处理第 23 批: scene [96, 97, 98]
2025-08-17 22:24:08,121 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [96, 97, 98]
