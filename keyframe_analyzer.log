2025-08-17 17:51:23,378 - 关键帧分析器 - INFO - 运行测试模式，只分析前2张图片
2025-08-17 17:51:23,378 - 关键帧分析器 - INFO - 开始扫描目录: F:\github\aicut_auto\ai-video-splitter\Keyframes
2025-08-17 17:51:23,445 - 关键帧分析器 - INFO - 找到 3221 个关键帧图片文件
2025-08-17 17:51:23,446 - 关键帧分析器 - INFO - 开始按scene编号排序图片文件
2025-08-17 17:51:23,452 - 关键帧分析器 - INFO - 成功排序 3221 个图片文件
2025-08-17 17:51:23,452 - 关键帧分析器 - INFO - Scene编号范围: 1 - 3221
2025-08-17 17:51:23,452 - 关键帧分析器 - INFO - 限制处理图片数量为: 2
2025-08-17 17:51:23,452 - 关键帧分析器 - INFO - 处理图片: scene001 - keyframe_scene001_start.png
2025-08-17 17:51:23,456 - 关键帧分析器 - INFO - 处理图片: scene002 - keyframe_scene002_start.png
2025-08-17 17:51:23,458 - 关键帧分析器 - INFO - 成功准备 2 张图片用于分析
2025-08-17 17:51:23,458 - 关键帧分析器 - INFO - 图片处理和排序功能测试完成
2025-08-17 17:51:23,458 - 关键帧分析器 - INFO - 准备分析的图片:
2025-08-17 17:51:23,458 - 关键帧分析器 - INFO -   - Scene 001: keyframe_scene001_start.png
2025-08-17 17:51:23,458 - 关键帧分析器 - INFO -   - Scene 002: keyframe_scene002_start.png
2025-08-17 17:51:23,458 - 关键帧分析器 - INFO - 
开始调用Gemini Balance API分析图片...
2025-08-17 17:51:23,458 - 关键帧分析器 - INFO - 开始调用Gemini Balance API分析 2 张图片
2025-08-17 17:51:23,460 - 关键帧分析器 - INFO - 正在调用API: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 17:51:31,463 - 关键帧分析器 - INFO - API调用成功，获得分析结果
2025-08-17 17:51:31,463 - 关键帧分析器 - INFO - API分析完成
2025-08-17 17:51:31,473 - 关键帧分析器 - INFO - 分析结果已保存到: F:\github\aicut_auto\keyframe_analysis_20250817_175131.json
2025-08-17 17:51:31,473 - 关键帧分析器 - INFO - 分析完成！结果已保存到: F:\github\aicut_auto\keyframe_analysis_20250817_175131.json
2025-08-17 17:51:31,475 - 关键帧分析器 - INFO - 
分析结果预览:
2025-08-17 17:51:31,475 - 关键帧分析器 - INFO - ==================================================
2025-08-17 17:51:31,475 - 关键帧分析器 - INFO - ```json
{
  "frame_observations": [
    {
      "frame_number": 1,
      "observation": "该帧展示了夜间或黄昏时分，一条笔直的柏油马路的俯瞰视角。马路两侧是茂密的绿色树木，路灯发出光亮照亮路面。路面上有清晰的白色车道线。画面的左侧垂直显示着“滨海大道”的文字，左上角有备案信息“（爱奇艺）网微剧备字（2025）第13509号”。画面整体清晰，没有人物活动。"
    },
    {
      "frame_number": 2,
      "observation": "该帧延续了第一帧的马路场景，但画面呈现出明显的动态模糊效果，表明有快速的运动。一个模糊的人形物体出现在马路中央，似乎正在快速移动，其轮廓因运动而拉伸。马路、车道线和两侧的树木也因运动模糊而变得不清晰。左上角的备案信息也因模糊而难以辨认，但位置与第一帧一致。整体场景从静态转变为动态。"
    }
  ],
  "overall_activity_summary": "视频片段首先展示了夜间滨海大道的静态全景，随后画面变得模糊，显...
2025-08-17 17:51:31,475 - 关键帧分析器 - INFO - ==================================================
2025-08-17 17:52:22,345 - 关键帧分析器 - INFO - 运行测试模式，只分析前2张图片
2025-08-17 17:52:22,345 - 关键帧分析器 - INFO - 开始扫描目录: F:\github\aicut_auto\ai-video-splitter\Keyframes
2025-08-17 17:52:22,351 - 关键帧分析器 - INFO - 找到 3221 个关键帧图片文件
2025-08-17 17:52:22,351 - 关键帧分析器 - INFO - 开始按scene编号排序图片文件
2025-08-17 17:52:22,358 - 关键帧分析器 - INFO - 成功排序 3221 个图片文件
2025-08-17 17:52:22,358 - 关键帧分析器 - INFO - Scene编号范围: 1 - 3221
2025-08-17 17:52:22,358 - 关键帧分析器 - INFO - 限制处理图片数量为: 2
2025-08-17 17:52:22,358 - 关键帧分析器 - INFO - 处理图片: scene001 - keyframe_scene001_start.png
2025-08-17 17:52:22,361 - 关键帧分析器 - INFO - 处理图片: scene002 - keyframe_scene002_start.png
2025-08-17 17:52:22,363 - 关键帧分析器 - INFO - 成功准备 2 张图片用于分析
2025-08-17 17:52:22,363 - 关键帧分析器 - INFO - 图片处理和排序功能测试完成
2025-08-17 17:52:22,364 - 关键帧分析器 - INFO - 准备分析的图片:
2025-08-17 17:52:22,364 - 关键帧分析器 - INFO -   - Scene 001: keyframe_scene001_start.png
2025-08-17 17:52:22,364 - 关键帧分析器 - INFO -   - Scene 002: keyframe_scene002_start.png
2025-08-17 17:52:22,364 - 关键帧分析器 - INFO - 
开始调用Gemini Balance API分析图片...
2025-08-17 17:52:22,364 - 关键帧分析器 - INFO - 开始调用Gemini Balance API分析 2 张图片
2025-08-17 17:52:22,364 - 关键帧分析器 - INFO - 正在调用API: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 17:52:31,412 - 关键帧分析器 - INFO - API调用成功，获得分析结果
2025-08-17 17:52:31,413 - 关键帧分析器 - INFO - API分析完成
2025-08-17 17:52:31,413 - 关键帧分析器 - INFO - 分析结果已保存到: F:\github\aicut_auto\keyframe_analysis_20250817_175231.json
2025-08-17 17:52:31,413 - 关键帧分析器 - INFO - 分析完成！结果已保存到: F:\github\aicut_auto\keyframe_analysis_20250817_175231.json
2025-08-17 17:52:31,414 - 关键帧分析器 - INFO - 
分析结果预览:
2025-08-17 17:52:31,414 - 关键帧分析器 - INFO - ==================================================
2025-08-17 17:52:31,414 - 关键帧分析器 - INFO - ```json
{
  "frame_observations": [
    {
      "frame_number": 1,
      "observation": "第一帧展示了夜间从高空俯瞰的沥青路面，路面有清晰的白色车道线。道路两侧是茂密的绿色树木，并有路灯发出微弱的光。画面左侧竖向显示有白色文字“滨海大道”，左上角有小字标注“（爱奇艺）网微剧备字（2025）第13509号”。画面中没有人或车辆，整体呈现一种宁静的静态景象。"
    },
    {
      "frame_number": 2,
      "observation": "第二帧是同一条道路的俯瞰视角，但画面呈现出明显的运动模糊效果，表明有快速的移动或镜头晃动。画面中央有一个模糊的、暗红色/粉色的物体，形状不规则，可能是一个人或一辆小型交通工具，正在快速移动。道路两侧的树木和路面线条也因运动而变得模糊。左上角的“爱奇艺”文字依然存在，但“滨海大道”的文字已消失，表明画面已进入动态内容阶段。"
    }
  ],
  "overall_activity_summary": "视频片段从一条夜间空旷...
2025-08-17 17:52:31,414 - 关键帧分析器 - INFO - ==================================================
2025-08-17 17:52:58,600 - 关键帧分析器 - INFO - 指定分析图片数量: 3
2025-08-17 17:52:58,600 - 关键帧分析器 - INFO - 开始扫描目录: F:\github\aicut_auto\ai-video-splitter\Keyframes
2025-08-17 17:52:58,606 - 关键帧分析器 - INFO - 找到 3221 个关键帧图片文件
2025-08-17 17:52:58,606 - 关键帧分析器 - INFO - 开始按scene编号排序图片文件
2025-08-17 17:52:58,612 - 关键帧分析器 - INFO - 成功排序 3221 个图片文件
2025-08-17 17:52:58,613 - 关键帧分析器 - INFO - Scene编号范围: 1 - 3221
2025-08-17 17:52:58,613 - 关键帧分析器 - INFO - 限制处理图片数量为: 3
2025-08-17 17:52:58,613 - 关键帧分析器 - INFO - 处理图片: scene001 - keyframe_scene001_start.png
2025-08-17 17:52:58,617 - 关键帧分析器 - INFO - 处理图片: scene002 - keyframe_scene002_start.png
2025-08-17 17:52:58,618 - 关键帧分析器 - INFO - 处理图片: scene003 - keyframe_scene003_start.png
2025-08-17 17:52:58,633 - 关键帧分析器 - INFO - 成功准备 3 张图片用于分析
2025-08-17 17:52:58,633 - 关键帧分析器 - INFO - 图片处理和排序功能测试完成
2025-08-17 17:52:58,633 - 关键帧分析器 - INFO - 准备分析的图片:
2025-08-17 17:52:58,633 - 关键帧分析器 - INFO -   - Scene 001: keyframe_scene001_start.png
2025-08-17 17:52:58,633 - 关键帧分析器 - INFO -   - Scene 002: keyframe_scene002_start.png
2025-08-17 17:52:58,633 - 关键帧分析器 - INFO -   - Scene 003: keyframe_scene003_start.png
2025-08-17 17:52:58,633 - 关键帧分析器 - INFO - 
开始调用Gemini Balance API分析图片...
2025-08-17 17:52:58,633 - 关键帧分析器 - INFO - 开始调用Gemini Balance API分析 3 张图片
2025-08-17 17:52:58,634 - 关键帧分析器 - INFO - 正在调用API: http://192.168.100.159:7770/v1/chat/completions
2025-08-17 17:53:07,046 - 关键帧分析器 - INFO - API调用成功，获得分析结果
2025-08-17 17:53:07,048 - 关键帧分析器 - INFO - API分析完成
2025-08-17 17:53:07,058 - 关键帧分析器 - INFO - 分析结果已保存到: F:\github\aicut_auto\keyframe_analysis_20250817_175307.json
2025-08-17 17:53:07,058 - 关键帧分析器 - INFO - 分析完成！结果已保存到: F:\github\aicut_auto\keyframe_analysis_20250817_175307.json
2025-08-17 17:53:07,058 - 关键帧分析器 - INFO - 
分析结果预览:
2025-08-17 17:53:07,058 - 关键帧分析器 - INFO - ==================================================
2025-08-17 17:53:07,059 - 关键帧分析器 - INFO - ```json
{
  "frame_observations": [
    {
      "frame_number": 1,
      "observation": "该帧展示了夜间或黄昏时分，一条铺设良好的柏油路面的俯瞰图。路面有清晰的白色车道线，两侧是茂密的绿色树木，并有路灯提供照明。画面左侧有中文文字“滨海大道”叠加，指示地点。路面上没有车辆或行人。"
    },
    {
      "frame_number": 2,
      "observation": "该帧显示了与第一帧相同的道路和环境，但画面呈现出强烈的运动模糊效果。路中央有一个模糊的、难以辨认的深色或红色物体，暗示有物体正在高速通过。整个场景因运动而变得模糊不清，无法辨识具体细节，但能感受到动态变化。"
    },
    {
      "frame_number": 3,
      "observation": "该帧清晰地捕捉到一名身穿黑色皮衣、戴着黑色头盔和手套的骑手，正驾驶一辆黑色运动型摩托车在路上行驶。摩托车的前灯明亮地亮着。场景仍然是夜间的道路，两侧有树木，光线较暗，但摩托车和骑...
2025-08-17 17:53:07,059 - 关键帧分析器 - INFO - ==================================================
2025-08-17 21:34:26,786 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:34:26,786 - 关键帧分析器 - INFO - 视频关键帧分析工具启动
2025-08-17 21:34:26,787 - 关键帧分析器 - INFO - 每批处理图片数量: 2
2025-08-17 21:34:26,787 - 关键帧分析器 - INFO - 最大重试次数: 3
2025-08-17 21:34:26,787 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:34:26,787 - 关键帧分析器 - INFO - 检查scenes_report.json文件...
2025-08-17 21:34:26,827 - 关键帧分析器 - INFO - 成功加载scenes_report.json，包含 3221 个场景
2025-08-17 21:34:26,827 - 关键帧分析器 - INFO - 开始扫描目录: F:\github\aicut_auto\ai-video-splitter\Keyframes
2025-08-17 21:34:26,833 - 关键帧分析器 - INFO - 找到 3221 个关键帧图片文件
2025-08-17 21:34:26,833 - 关键帧分析器 - INFO - 开始按scene编号排序图片文件
2025-08-17 21:34:26,840 - 关键帧分析器 - INFO - 成功排序 3221 个图片文件
2025-08-17 21:34:26,840 - 关键帧分析器 - INFO - Scene编号范围: 1 - 3221
2025-08-17 21:34:26,840 - 关键帧分析器 - INFO - 限制处理图片数量为: 4
2025-08-17 21:34:26,840 - 关键帧分析器 - INFO - 准备处理 4 张图片
2025-08-17 21:34:26,840 - 关键帧分析器 - INFO - 开始分批处理 4 张图片，共 2 批，每批 2 张
2025-08-17 21:34:26,849 - 关键帧分析器 - INFO - 
处理第 1/2 批: scene [1, 2]
2025-08-17 21:34:26,849 - 关键帧分析器 - INFO -   准备图片: scene001 - keyframe_scene001_start.png
2025-08-17 21:34:26,853 - 关键帧分析器 - INFO -   准备图片: scene002 - keyframe_scene002_start.png
2025-08-17 21:34:26,855 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [1, 2]
2025-08-17 21:34:34,656 - 关键帧分析器 - INFO - API响应验证通过，包含2个有效的observation
2025-08-17 21:34:34,656 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:34:34,657 - 关键帧分析器 - WARNING - 未找到scene_id 1 对应的场景
2025-08-17 21:34:34,657 - 关键帧分析器 - WARNING - 未找到scene_id 2 对应的场景
2025-08-17 21:34:34,657 - 关键帧分析器 - INFO - 成功更新 0 个场景的observation
2025-08-17 21:34:34,657 - 关键帧分析器 - ERROR - 第 1 批更新JSON失败
2025-08-17 21:34:34,657 - 关键帧分析器 - INFO - 
处理第 2/2 批: scene [3, 4]
2025-08-17 21:34:34,658 - 关键帧分析器 - INFO -   准备图片: scene003 - keyframe_scene003_start.png
2025-08-17 21:34:34,661 - 关键帧分析器 - INFO -   准备图片: scene004 - keyframe_scene004_start.png
2025-08-17 21:34:34,663 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [3, 4]
2025-08-17 21:34:42,296 - 关键帧分析器 - INFO - API响应验证通过，包含2个有效的observation
2025-08-17 21:34:42,296 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:34:42,297 - 关键帧分析器 - WARNING - 未找到scene_id 3 对应的场景
2025-08-17 21:34:42,298 - 关键帧分析器 - WARNING - 未找到scene_id 4 对应的场景
2025-08-17 21:34:42,298 - 关键帧分析器 - INFO - 成功更新 0 个场景的observation
2025-08-17 21:34:42,298 - 关键帧分析器 - ERROR - 第 2 批更新JSON失败
2025-08-17 21:34:42,298 - 关键帧分析器 - INFO - 
分批处理完成: 成功 0/2 批
2025-08-17 21:34:42,298 - 关键帧分析器 - ERROR - 失败的scene_id: [1, 2, 3, 4]
2025-08-17 21:34:42,299 - 关键帧分析器 - INFO - 
============================================================
2025-08-17 21:34:42,299 - 关键帧分析器 - INFO - 处理完成报告
2025-08-17 21:34:42,299 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:34:42,299 - 关键帧分析器 - INFO - 总图片数量: 4
2025-08-17 21:34:42,299 - 关键帧分析器 - INFO - 成功处理批次: 0
2025-08-17 21:34:42,299 - 关键帧分析器 - ERROR - 失败的scene_id数量: 4
2025-08-17 21:34:42,299 - 关键帧分析器 - ERROR - 失败的scene_id列表: [1, 2, 3, 4]
2025-08-17 21:34:42,299 - 关键帧分析器 - ERROR - 建议检查:
2025-08-17 21:34:42,299 - 关键帧分析器 - ERROR - 1. 网络连接是否正常
2025-08-17 21:34:42,299 - 关键帧分析器 - ERROR - 2. API服务是否可用
2025-08-17 21:34:42,299 - 关键帧分析器 - ERROR - 3. 图片文件是否损坏
2025-08-17 21:34:42,300 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:35:12,792 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:35:12,792 - 关键帧分析器 - INFO - 视频关键帧分析工具启动
2025-08-17 21:35:12,792 - 关键帧分析器 - INFO - 每批处理图片数量: 2
2025-08-17 21:35:12,792 - 关键帧分析器 - INFO - 最大重试次数: 3
2025-08-17 21:35:12,792 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:35:12,793 - 关键帧分析器 - INFO - 检查scenes_report.json文件...
2025-08-17 21:35:12,833 - 关键帧分析器 - INFO - 成功加载scenes_report.json，包含 3221 个场景
2025-08-17 21:35:12,833 - 关键帧分析器 - INFO - 开始扫描目录: F:\github\aicut_auto\ai-video-splitter\Keyframes
2025-08-17 21:35:12,837 - 关键帧分析器 - INFO - 找到 3221 个关键帧图片文件
2025-08-17 21:35:12,839 - 关键帧分析器 - INFO - 开始按scene编号排序图片文件
2025-08-17 21:35:12,845 - 关键帧分析器 - INFO - 成功排序 3221 个图片文件
2025-08-17 21:35:12,845 - 关键帧分析器 - INFO - Scene编号范围: 1 - 3221
2025-08-17 21:35:12,845 - 关键帧分析器 - INFO - 限制处理图片数量为: 4
2025-08-17 21:35:12,845 - 关键帧分析器 - INFO - 准备处理 4 张图片
2025-08-17 21:35:12,845 - 关键帧分析器 - INFO - 开始分批处理 4 张图片，共 2 批，每批 2 张
2025-08-17 21:35:12,845 - 关键帧分析器 - INFO - 
处理第 1/2 批: scene [1, 2]
2025-08-17 21:35:12,845 - 关键帧分析器 - INFO -   准备图片: scene001 - keyframe_scene001_start.png
2025-08-17 21:35:12,849 - 关键帧分析器 - INFO -   准备图片: scene002 - keyframe_scene002_start.png
2025-08-17 21:35:12,851 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [1, 2]
2025-08-17 21:35:20,224 - 关键帧分析器 - INFO - API响应验证通过，包含2个有效的observation
2025-08-17 21:35:20,224 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:35:20,224 - 关键帧分析器 - INFO - 已更新scene_id 1 的observation
2025-08-17 21:35:20,224 - 关键帧分析器 - INFO - 已更新scene_id 2 的observation
2025-08-17 21:35:20,224 - 关键帧分析器 - INFO - 成功更新 2 个场景的observation
2025-08-17 21:35:20,225 - 关键帧分析器 - INFO - 第 1 批处理成功
2025-08-17 21:35:20,225 - 关键帧分析器 - INFO - 
处理第 2/2 批: scene [3, 4]
2025-08-17 21:35:20,225 - 关键帧分析器 - INFO -   准备图片: scene003 - keyframe_scene003_start.png
2025-08-17 21:35:20,228 - 关键帧分析器 - INFO -   准备图片: scene004 - keyframe_scene004_start.png
2025-08-17 21:35:20,230 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [3, 4]
2025-08-17 21:35:28,941 - 关键帧分析器 - INFO - API响应验证通过，包含2个有效的observation
2025-08-17 21:35:28,941 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:35:28,941 - 关键帧分析器 - INFO - 已更新scene_id 3 的observation
2025-08-17 21:35:28,941 - 关键帧分析器 - INFO - 已更新scene_id 4 的observation
2025-08-17 21:35:28,941 - 关键帧分析器 - INFO - 成功更新 2 个场景的observation
2025-08-17 21:35:28,942 - 关键帧分析器 - INFO - 第 2 批处理成功
2025-08-17 21:35:28,942 - 关键帧分析器 - INFO - 
分批处理完成: 成功 2/2 批
2025-08-17 21:35:29,096 - 关键帧分析器 - INFO - 成功保存scenes_report.json: F:\github\aicut_auto\ai-video-splitter\scenes_report.json
2025-08-17 21:35:29,097 - 关键帧分析器 - INFO - scenes_report.json已成功更新
2025-08-17 21:35:29,097 - 关键帧分析器 - INFO - 
============================================================
2025-08-17 21:35:29,097 - 关键帧分析器 - INFO - 处理完成报告
2025-08-17 21:35:29,097 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:35:29,097 - 关键帧分析器 - INFO - 总图片数量: 4
2025-08-17 21:35:29,097 - 关键帧分析器 - INFO - 成功处理批次: 2
2025-08-17 21:35:29,097 - 关键帧分析器 - INFO - 所有图片处理成功！
2025-08-17 21:35:29,097 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:37:59,144 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:37:59,144 - 关键帧分析器 - INFO - 视频关键帧分析工具启动
2025-08-17 21:37:59,144 - 关键帧分析器 - INFO - 每批处理图片数量: 3
2025-08-17 21:37:59,144 - 关键帧分析器 - INFO - 最大重试次数: 3
2025-08-17 21:37:59,144 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:37:59,144 - 关键帧分析器 - INFO - 检查scenes_report.json文件...
2025-08-17 21:37:59,196 - 关键帧分析器 - INFO - 成功加载scenes_report.json，包含 3221 个场景
2025-08-17 21:37:59,196 - 关键帧分析器 - INFO - 开始扫描目录: F:\github\aicut_auto\ai-video-splitter\Keyframes
2025-08-17 21:37:59,201 - 关键帧分析器 - INFO - 找到 3221 个关键帧图片文件
2025-08-17 21:37:59,202 - 关键帧分析器 - INFO - 开始按scene编号排序图片文件
2025-08-17 21:37:59,209 - 关键帧分析器 - INFO - 成功排序 3221 个图片文件
2025-08-17 21:37:59,209 - 关键帧分析器 - INFO - Scene编号范围: 1 - 3221
2025-08-17 21:37:59,209 - 关键帧分析器 - INFO - 限制处理图片数量为: 6
2025-08-17 21:37:59,209 - 关键帧分析器 - INFO - 准备处理 6 张图片
2025-08-17 21:37:59,209 - 关键帧分析器 - INFO - 开始分批处理 6 张图片，共 2 批，每批 3 张
2025-08-17 21:37:59,209 - 关键帧分析器 - INFO - 
处理第 1/2 批: scene [1, 2, 3]
2025-08-17 21:37:59,209 - 关键帧分析器 - INFO -   准备图片: scene001 - keyframe_scene001_start.png
2025-08-17 21:37:59,213 - 关键帧分析器 - INFO -   准备图片: scene002 - keyframe_scene002_start.png
2025-08-17 21:37:59,215 - 关键帧分析器 - INFO -   准备图片: scene003 - keyframe_scene003_start.png
2025-08-17 21:37:59,218 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [1, 2, 3]
2025-08-17 21:38:08,407 - 关键帧分析器 - INFO - API响应验证通过，包含3个有效的observation
2025-08-17 21:38:08,407 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:38:08,407 - 关键帧分析器 - INFO - 已更新scene_id 1 的observation
2025-08-17 21:38:08,408 - 关键帧分析器 - INFO - 已更新scene_id 2 的observation
2025-08-17 21:38:08,408 - 关键帧分析器 - INFO - 已更新scene_id 3 的observation
2025-08-17 21:38:08,408 - 关键帧分析器 - INFO - 成功更新 3 个场景的observation
2025-08-17 21:38:08,408 - 关键帧分析器 - INFO - 第 1 批处理成功
2025-08-17 21:38:08,408 - 关键帧分析器 - INFO - 
处理第 2/2 批: scene [4, 5, 6]
2025-08-17 21:38:08,408 - 关键帧分析器 - INFO -   准备图片: scene004 - keyframe_scene004_start.png
2025-08-17 21:38:08,410 - 关键帧分析器 - INFO -   准备图片: scene005 - keyframe_scene005_start.png
2025-08-17 21:38:08,412 - 关键帧分析器 - INFO -   准备图片: scene006 - keyframe_scene006_start.png
2025-08-17 21:38:08,413 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [4, 5, 6]
2025-08-17 21:38:17,341 - 关键帧分析器 - INFO - API响应验证通过，包含3个有效的observation
2025-08-17 21:38:17,341 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:38:17,342 - 关键帧分析器 - INFO - 已更新scene_id 4 的observation
2025-08-17 21:38:17,342 - 关键帧分析器 - INFO - 已更新scene_id 5 的observation
2025-08-17 21:38:17,342 - 关键帧分析器 - INFO - 已更新scene_id 6 的observation
2025-08-17 21:38:17,342 - 关键帧分析器 - INFO - 成功更新 3 个场景的observation
2025-08-17 21:38:17,342 - 关键帧分析器 - INFO - 第 2 批处理成功
2025-08-17 21:38:17,342 - 关键帧分析器 - INFO - 
分批处理完成: 成功 2/2 批
2025-08-17 21:38:17,489 - 关键帧分析器 - INFO - 成功保存scenes_report.json: F:\github\aicut_auto\ai-video-splitter\scenes_report.json
2025-08-17 21:38:17,489 - 关键帧分析器 - INFO - scenes_report.json已成功更新
2025-08-17 21:38:17,490 - 关键帧分析器 - INFO - 
============================================================
2025-08-17 21:38:17,490 - 关键帧分析器 - INFO - 处理完成报告
2025-08-17 21:38:17,490 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:38:17,490 - 关键帧分析器 - INFO - 总图片数量: 6
2025-08-17 21:38:17,490 - 关键帧分析器 - INFO - 成功处理批次: 2
2025-08-17 21:38:17,490 - 关键帧分析器 - INFO - 所有图片处理成功！
2025-08-17 21:38:17,490 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:45:22,446 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:45:22,446 - 关键帧分析器 - INFO - 视频关键帧分析工具启动
2025-08-17 21:45:22,446 - 关键帧分析器 - INFO - 每批处理图片数量: 3
2025-08-17 21:45:22,446 - 关键帧分析器 - INFO - 最大重试次数: 3
2025-08-17 21:45:22,447 - 关键帧分析器 - INFO - 并行线程数: 2
2025-08-17 21:45:22,447 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:45:22,447 - 关键帧分析器 - INFO - 检查scenes_report.json文件...
2025-08-17 21:45:22,487 - 关键帧分析器 - INFO - 成功加载scenes_report.json，包含 3221 个场景
2025-08-17 21:45:22,487 - 关键帧分析器 - INFO - 开始扫描目录: F:\github\aicut_auto\ai-video-splitter\Keyframes
2025-08-17 21:45:22,492 - 关键帧分析器 - INFO - 找到 3221 个关键帧图片文件
2025-08-17 21:45:22,492 - 关键帧分析器 - INFO - 开始按scene编号排序图片文件
2025-08-17 21:45:22,498 - 关键帧分析器 - INFO - 成功排序 3221 个图片文件
2025-08-17 21:45:22,498 - 关键帧分析器 - INFO - Scene编号范围: 1 - 3221
2025-08-17 21:45:22,499 - 关键帧分析器 - INFO - 限制处理图片数量为: 6
2025-08-17 21:45:22,499 - 关键帧分析器 - INFO - 准备处理 6 张图片
2025-08-17 21:45:22,499 - 关键帧分析器 - INFO - 开始并行分批处理 6 张图片，共 2 批，每批 3 张
2025-08-17 21:45:22,499 - 关键帧分析器 - INFO - 使用 2 个并行线程
2025-08-17 21:45:22,499 - 关键帧分析器 - INFO - 线程处理第 1 批: scene [1, 2, 3]
2025-08-17 21:45:22,499 - 关键帧分析器 - INFO - 线程处理第 2 批: scene [4, 5, 6]
2025-08-17 21:45:22,510 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [4, 5, 6]
2025-08-17 21:45:22,512 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [1, 2, 3]
2025-08-17 21:45:31,620 - 关键帧分析器 - INFO - API响应验证通过，包含3个有效的observation
2025-08-17 21:45:31,621 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:45:31,621 - 关键帧分析器 - INFO - 已更新scene_id 1 的observation
2025-08-17 21:45:31,621 - 关键帧分析器 - INFO - 已更新scene_id 2 的observation
2025-08-17 21:45:31,621 - 关键帧分析器 - INFO - 已更新scene_id 3 的observation
2025-08-17 21:45:31,621 - 关键帧分析器 - INFO - 成功更新 3 个场景的observation
2025-08-17 21:45:31,622 - 关键帧分析器 - INFO - 第 1 批处理成功
2025-08-17 21:45:32,599 - 关键帧分析器 - INFO - API响应验证通过，包含3个有效的observation
2025-08-17 21:45:32,599 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:45:32,600 - 关键帧分析器 - INFO - 已更新scene_id 4 的observation
2025-08-17 21:45:32,600 - 关键帧分析器 - INFO - 已更新scene_id 5 的observation
2025-08-17 21:45:32,600 - 关键帧分析器 - INFO - 已更新scene_id 6 的observation
2025-08-17 21:45:32,600 - 关键帧分析器 - INFO - 成功更新 3 个场景的observation
2025-08-17 21:45:32,600 - 关键帧分析器 - INFO - 第 2 批处理成功
2025-08-17 21:45:32,601 - 关键帧分析器 - INFO - 
并行处理完成: 成功 2/2 批
2025-08-17 21:45:32,752 - 关键帧分析器 - INFO - 成功保存scenes_report.json: F:\github\aicut_auto\ai-video-splitter\scenes_report.json
2025-08-17 21:45:32,752 - 关键帧分析器 - INFO - scenes_report.json已成功更新
2025-08-17 21:45:32,752 - 关键帧分析器 - INFO - 
============================================================
2025-08-17 21:45:32,752 - 关键帧分析器 - INFO - 处理完成报告
2025-08-17 21:45:32,752 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:45:32,753 - 关键帧分析器 - INFO - 总图片数量: 6
2025-08-17 21:45:32,753 - 关键帧分析器 - INFO - 成功处理批次: 2
2025-08-17 21:45:32,753 - 关键帧分析器 - INFO - 所有图片处理成功！
2025-08-17 21:45:32,753 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:46:22,616 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:46:22,616 - 关键帧分析器 - INFO - 视频关键帧分析工具启动
2025-08-17 21:46:22,616 - 关键帧分析器 - INFO - 每批处理图片数量: 2
2025-08-17 21:46:22,617 - 关键帧分析器 - INFO - 最大重试次数: 3
2025-08-17 21:46:22,617 - 关键帧分析器 - INFO - 并行线程数: 5
2025-08-17 21:46:22,617 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:46:22,617 - 关键帧分析器 - INFO - 检查scenes_report.json文件...
2025-08-17 21:46:22,657 - 关键帧分析器 - INFO - 成功加载scenes_report.json，包含 3221 个场景
2025-08-17 21:46:22,658 - 关键帧分析器 - INFO - 开始扫描目录: F:\github\aicut_auto\ai-video-splitter\Keyframes
2025-08-17 21:46:22,663 - 关键帧分析器 - INFO - 找到 3221 个关键帧图片文件
2025-08-17 21:46:22,663 - 关键帧分析器 - INFO - 开始按scene编号排序图片文件
2025-08-17 21:46:22,669 - 关键帧分析器 - INFO - 成功排序 3221 个图片文件
2025-08-17 21:46:22,669 - 关键帧分析器 - INFO - Scene编号范围: 1 - 3221
2025-08-17 21:46:22,670 - 关键帧分析器 - INFO - 限制处理图片数量为: 10
2025-08-17 21:46:22,670 - 关键帧分析器 - INFO - 准备处理 10 张图片
2025-08-17 21:46:22,670 - 关键帧分析器 - INFO - 开始并行分批处理 10 张图片，共 5 批，每批 2 张
2025-08-17 21:46:22,670 - 关键帧分析器 - INFO - 使用 5 个并行线程
2025-08-17 21:46:22,670 - 关键帧分析器 - INFO - 线程处理第 1 批: scene [1, 2]
2025-08-17 21:46:22,670 - 关键帧分析器 - INFO - 线程处理第 2 批: scene [3, 4]
2025-08-17 21:46:22,671 - 关键帧分析器 - INFO - 线程处理第 3 批: scene [5, 6]
2025-08-17 21:46:22,671 - 关键帧分析器 - INFO - 线程处理第 4 批: scene [7, 8]
2025-08-17 21:46:22,671 - 关键帧分析器 - INFO - 线程处理第 5 批: scene [9, 10]
2025-08-17 21:46:22,684 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [1, 2]
2025-08-17 21:46:22,688 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [3, 4]
2025-08-17 21:46:22,694 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [9, 10]
2025-08-17 21:46:22,694 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [5, 6]
2025-08-17 21:46:22,694 - 关键帧分析器 - INFO - 第1次尝试调用API分析scene [7, 8]
2025-08-17 21:46:30,269 - 关键帧分析器 - INFO - API响应验证通过，包含2个有效的observation
2025-08-17 21:46:30,269 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:46:30,269 - 关键帧分析器 - INFO - 已更新scene_id 5 的observation
2025-08-17 21:46:30,270 - 关键帧分析器 - INFO - 已更新scene_id 6 的observation
2025-08-17 21:46:30,270 - 关键帧分析器 - INFO - 成功更新 2 个场景的observation
2025-08-17 21:46:30,270 - 关键帧分析器 - INFO - 第 3 批处理成功
2025-08-17 21:46:30,460 - 关键帧分析器 - INFO - API响应验证通过，包含2个有效的observation
2025-08-17 21:46:30,460 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:46:30,462 - 关键帧分析器 - INFO - 已更新scene_id 3 的observation
2025-08-17 21:46:30,462 - 关键帧分析器 - INFO - 已更新scene_id 4 的observation
2025-08-17 21:46:30,462 - 关键帧分析器 - INFO - 成功更新 2 个场景的observation
2025-08-17 21:46:30,463 - 关键帧分析器 - INFO - 第 2 批处理成功
2025-08-17 21:46:32,512 - 关键帧分析器 - INFO - API响应验证通过，包含2个有效的observation
2025-08-17 21:46:32,512 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:46:32,514 - 关键帧分析器 - INFO - 已更新scene_id 1 的observation
2025-08-17 21:46:32,514 - 关键帧分析器 - INFO - 已更新scene_id 2 的observation
2025-08-17 21:46:32,514 - 关键帧分析器 - INFO - 成功更新 2 个场景的observation
2025-08-17 21:46:32,514 - 关键帧分析器 - INFO - 第 1 批处理成功
2025-08-17 21:46:32,669 - 关键帧分析器 - INFO - API响应验证通过，包含2个有效的observation
2025-08-17 21:46:32,669 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:46:32,670 - 关键帧分析器 - INFO - 已更新scene_id 7 的observation
2025-08-17 21:46:32,670 - 关键帧分析器 - INFO - 已更新scene_id 8 的observation
2025-08-17 21:46:32,670 - 关键帧分析器 - INFO - 成功更新 2 个场景的observation
2025-08-17 21:46:32,670 - 关键帧分析器 - INFO - 第 4 批处理成功
2025-08-17 21:46:35,576 - 关键帧分析器 - INFO - API响应验证通过，包含2个有效的observation
2025-08-17 21:46:35,576 - 关键帧分析器 - INFO - 第1次尝试成功，获得有效的分析结果
2025-08-17 21:46:35,577 - 关键帧分析器 - INFO - 已更新scene_id 9 的observation
2025-08-17 21:46:35,577 - 关键帧分析器 - INFO - 已更新scene_id 10 的observation
2025-08-17 21:46:35,577 - 关键帧分析器 - INFO - 成功更新 2 个场景的observation
2025-08-17 21:46:35,577 - 关键帧分析器 - INFO - 第 5 批处理成功
2025-08-17 21:46:35,578 - 关键帧分析器 - INFO - 
并行处理完成: 成功 5/5 批
2025-08-17 21:46:35,718 - 关键帧分析器 - INFO - 成功保存scenes_report.json: F:\github\aicut_auto\ai-video-splitter\scenes_report.json
2025-08-17 21:46:35,718 - 关键帧分析器 - INFO - scenes_report.json已成功更新
2025-08-17 21:46:35,718 - 关键帧分析器 - INFO - 
============================================================
2025-08-17 21:46:35,719 - 关键帧分析器 - INFO - 处理完成报告
2025-08-17 21:46:35,719 - 关键帧分析器 - INFO - ============================================================
2025-08-17 21:46:35,719 - 关键帧分析器 - INFO - 总图片数量: 10
2025-08-17 21:46:35,719 - 关键帧分析器 - INFO - 成功处理批次: 5
2025-08-17 21:46:35,719 - 关键帧分析器 - INFO - 所有图片处理成功！
2025-08-17 21:46:35,719 - 关键帧分析器 - INFO - ============================================================
