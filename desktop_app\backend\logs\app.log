2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  90%|████████▉ | 96/107 [00:01<00:00, 52.15it/s][A[A[A[A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  12%|█▏        | 14/117 [00:00<00:01, 56.10it/s]
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  90%|█████████ | 95/105 [00:01<00:00, 51.47it/s][A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:   9%|▉         | 11/125 [00:00<00:02, 47.13it/s][A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  76%|███████▌  | 65/86 [00:01<00:00, 43.15it/s][A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  12%|█▏        | 11/92 [00:00<00:01, 51.94it/s][A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:   4%|▍         | 5/120 [00:00<00:02, 49.93it/s][A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  94%|█████████▍| 107/114 [00:02<00:00, 47.03it/s][A[A[A[A[A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  54%|█████▍    | 59/109 [00:01<00:01, 48.11it/s][A[A[A✓ 视频 38_1 合成和音频添加完成: F:/github/aicut_auto/water_remove\38_1_remove_with_audio.mp4
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: --- 开始合成视频: 9_1 ---
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: === 开始视频合成: F:/github/aicut_auto/water_remove\9_1_remove.mp4 ===
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 帧文件数量: 119
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 分辨率: 720x1280, 帧率: 25.0
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次大小: 119
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:   0%|          | 0/119 [00:00<?, ?it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  41%|████      | 41/100 [00:00<00:01, 49.84it/s][A[A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  79%|███████▉  | 72/91 [00:01<00:00, 48.25it/s][A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  95%|█████████▌| 102/107 [00:01<00:00, 52.17it/s][A[A[A[A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  17%|█▋        | 20/117 [00:00<00:01, 55.53it/s]
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  13%|█▎        | 16/125 [00:00<00:02, 46.74it/s][A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  96%|█████████▌| 101/105 [00:02<00:00, 50.67it/s][A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  98%|█████████▊| 112/114 [00:02<00:00, 47.19it/s][A[A[A[A[A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  81%|████████▏ | 70/86 [00:01<00:00, 42.57it/s][A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  18%|█▊        | 17/92 [00:00<00:01, 51.61it/s][A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  60%|█████▉    | 65/109 [00:01<00:00, 50.38it/s][A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:   5%|▌         | 6/119 [00:00<00:02, 55.77it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:   9%|▉         | 11/120 [00:00<00:02, 49.29it/s][A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  47%|████▋     | 47/100 [00:00<00:01, 49.73it/s][A[A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1: 100%|██████████| 114/114 [00:02<00:00, 45.23it/s]
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  86%|████████▌ | 78/91 [00:01<00:00, 48.45it/s][A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1: 100%|██████████| 105/105 [00:02<00:00, 49.69it/s]
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1: 100%|██████████| 107/107 [00:02<00:00, 52.90it/s]
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  17%|█▋        | 21/125 [00:00<00:02, 46.74it/s][A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  22%|██▏       | 26/117 [00:00<00:01, 50.53it/s]
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  13%|█▎        | 16/120 [00:00<00:02, 47.09it/s][A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  87%|████████▋ | 75/86 [00:01<00:00, 41.48it/s][A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  25%|██▌       | 23/92 [00:00<00:01, 49.35it/s][A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  52%|█████▏    | 52/100 [00:01<00:00, 48.74it/s][A[A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  65%|██████▌   | 71/109 [00:01<00:00, 48.82it/s][A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  10%|█         | 12/119 [00:00<00:02, 48.73it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  92%|█████████▏| 84/91 [00:01<00:00, 49.47it/s][A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  21%|██        | 26/125 [00:00<00:02, 45.71it/s][A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  27%|██▋       | 32/117 [00:00<00:01, 49.59it/s]
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  18%|█▊        | 21/120 [00:00<00:02, 47.26it/s][A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  32%|███▏      | 29/92 [00:00<00:01, 51.32it/s][A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  93%|█████████▎| 80/86 [00:01<00:00, 41.90it/s][A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  71%|███████   | 77/109 [00:01<00:00, 49.92it/s][A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  58%|█████▊    | 58/100 [00:01<00:00, 49.63it/s][A[A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  15%|█▌        | 18/119 [00:00<00:02, 49.34it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  99%|█████████▉| 90/91 [00:01<00:00, 51.31it/s][A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1: 100%|██████████| 91/91 [00:01<00:00, 46.72it/s]
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  26%|██▌       | 32/125 [00:00<00:01, 47.84it/s][A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  32%|███▏      | 37/117 [00:00<00:01, 49.16it/s]
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  22%|██▎       | 27/120 [00:00<00:01, 49.11it/s][A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  99%|█████████▉| 85/86 [00:01<00:00, 43.26it/s][A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  38%|███▊      | 35/92 [00:00<00:01, 51.70it/s][A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  63%|██████▎   | 63/100 [00:01<00:00, 49.34it/s][A[A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1: 100%|██████████| 86/86 [00:01<00:00, 43.55it/s]
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  77%|███████▋  | 84/109 [00:01<00:00, 52.66it/s][A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  20%|██        | 24/119 [00:00<00:01, 50.53it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  30%|███       | 38/125 [00:00<00:01, 50.17it/s][A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  36%|███▌      | 42/117 [00:00<00:01, 49.08it/s]
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  28%|██▊       | 33/120 [00:00<00:01, 50.26it/s][A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  69%|██████▉   | 69/100 [00:01<00:00, 51.58it/s][A[A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  45%|████▍     | 41/92 [00:00<00:00, 53.17it/s][A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  83%|████████▎ | 90/109 [00:01<00:00, 52.86it/s][A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  25%|██▌       | 30/119 [00:00<00:01, 50.95it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  35%|███▌      | 44/125 [00:00<00:01, 52.75it/s][A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  40%|████      | 47/117 [00:00<00:01, 48.98it/s]
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  32%|███▎      | 39/120 [00:00<00:01, 52.02it/s][A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  75%|███████▌  | 75/100 [00:01<00:00, 53.14it/s][A[A[A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  51%|█████     | 47/92 [00:00<00:00, 53.29it/s][A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  89%|████████▉ | 97/109 [00:01<00:00, 55.79it/s][A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  30%|███       | 36/119 [00:00<00:01, 50.33it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  40%|████      | 50/125 [00:00<00:01, 54.34it/s][A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  44%|████▍     | 52/117 [00:01<00:01, 48.27it/s]
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  38%|███▊      | 45/120 [00:00<00:01, 54.29it/s][A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  81%|████████  | 81/100 [00:01<00:00, 53.41it/s][A[A[A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  58%|█████▊    | 53/92 [00:01<00:00, 52.77it/s][A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  94%|█████████▍| 103/109 [00:02<00:00, 55.66it/s][A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  35%|███▌      | 42/119 [00:00<00:01, 51.37it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  45%|████▍     | 56/125 [00:01<00:01, 54.47it/s][A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  49%|████▊     | 57/117 [00:01<00:01, 47.89it/s]
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  42%|████▎     | 51/120 [00:00<00:01, 55.27it/s][A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  87%|████████▋ | 87/100 [00:01<00:00, 53.61it/s][A[A[A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  64%|██████▍   | 59/92 [00:01<00:00, 54.13it/s][A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1: 100%|██████████| 109/109 [00:02<00:00, 56.22it/s][A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1: 100%|██████████| 109/109 [00:02<00:00, 50.69it/s]
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  50%|████▉     | 62/125 [00:01<00:01, 55.19it/s][A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  40%|████      | 48/119 [00:00<00:01, 51.92it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  53%|█████▎    | 62/117 [00:01<00:01, 47.24it/s]
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  48%|████▊     | 57/120 [00:01<00:01, 54.25it/s][A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  93%|█████████▎| 93/100 [00:01<00:00, 54.33it/s][A[A[A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  71%|███████   | 65/92 [00:01<00:00, 55.64it/s][A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  54%|█████▍    | 68/125 [00:01<00:01, 54.40it/s][A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  45%|████▌     | 54/119 [00:01<00:01, 51.90it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  58%|█████▊    | 68/117 [00:01<00:00, 49.46it/s]
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  53%|█████▎    | 64/120 [00:01<00:00, 57.73it/s][A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  99%|█████████▉| 99/100 [00:01<00:00, 54.88it/s][A[A[A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  77%|███████▋  | 71/92 [00:01<00:00, 55.80it/s][A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1: 100%|██████████| 100/100 [00:01<00:00, 51.75it/s]
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  59%|█████▉    | 74/125 [00:01<00:00, 53.77it/s][A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  62%|██████▏   | 73/117 [00:01<00:00, 49.19it/s]
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  50%|█████     | 60/119 [00:01<00:01, 52.12it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  59%|█████▉    | 71/120 [00:01<00:00, 59.80it/s][A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  85%|████████▍ | 78/92 [00:01<00:00, 57.34it/s][A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  64%|██████▍   | 80/125 [00:01<00:00, 54.75it/s][A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  68%|██████▊   | 79/117 [00:01<00:00, 49.84it/s]
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  56%|█████▋    | 67/119 [00:01<00:00, 54.62it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  64%|██████▍   | 77/120 [00:01<00:00, 59.49it/s][A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  91%|█████████▏| 84/92 [00:01<00:00, 57.98it/s][A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  69%|██████▉   | 86/125 [00:01<00:00, 56.05it/s][A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  62%|██████▏   | 74/119 [00:01<00:00, 57.46it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  73%|███████▎  | 85/117 [00:01<00:00, 50.93it/s]
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  69%|██████▉   | 83/120 [00:01<00:00, 59.50it/s][A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  98%|█████████▊| 90/92 [00:01<00:00, 58.52it/s][A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1: 100%|██████████| 92/92 [00:01<00:00, 54.60it/s]
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  74%|███████▎  | 92/125 [00:01<00:00, 56.77it/s][A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  79%|███████▊  | 92/117 [00:01<00:00, 55.60it/s]
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  68%|██████▊   | 81/119 [00:01<00:00, 59.81it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  75%|███████▌  | 90/120 [00:01<00:00, 61.31it/s][A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  79%|███████▉  | 99/125 [00:01<00:00, 58.01it/s][A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  74%|███████▍  | 88/119 [00:01<00:00, 61.70it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  85%|████████▌ | 100/117 [00:01<00:00, 60.29it/s]
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  81%|████████  | 97/120 [00:01<00:00, 62.69it/s][A[A[A[A[A[A
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  85%|████████▍ | 106/125 [00:01<00:00, 58.79it/s][A
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  80%|███████▉  | 95/119 [00:01<00:00, 63.76it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  91%|█████████▏| 107/117 [00:02<00:00, 62.81it/s]
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  87%|████████▋ | 104/120 [00:01<00:00, 61.23it/s][A[A[A[A[A[A
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  90%|████████▉ | 112/125 [00:02<00:00, 58.98it/s][A
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  87%|████████▋ | 103/119 [00:01<00:00, 65.79it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  97%|█████████▋| 114/117 [00:02<00:00, 62.65it/s]
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  92%|█████████▎| 111/120 [00:01<00:00, 61.89it/s][A[A[A[A[A[A
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1: 100%|██████████| 117/117 [00:02<00:00, 54.11it/s]
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  94%|█████████▍| 118/125 [00:02<00:00, 58.41it/s][A
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  92%|█████████▏| 110/119 [00:01<00:00, 65.54it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  98%|█████████▊| 118/120 [00:02<00:00, 62.45it/s][A[A[A[A[A[A
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1: 100%|██████████| 120/120 [00:02<00:00, 57.10it/s]
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1: 100%|██████████| 125/125 [00:02<00:00, 59.32it/s][A
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1: 100%|██████████| 125/125 [00:02<00:00, 54.48it/s]
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  98%|█████████▊| 117/119 [00:02<00:00, 66.68it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1: 100%|██████████| 119/119 [00:02<00:00, 57.66it/s]
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 39_1 合成和音频添加完成: F:/github/aicut_auto/water_remove\39_1_remove_with_audio.mp4
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 37_1 合成和音频添加完成: F:/github/aicut_auto/water_remove\37_1_remove_with_audio.mp4
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 34_1 合成和音频添加完成: F:/github/aicut_auto/water_remove\34_1_remove_with_audio.mp4
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 34_1 完整处理成功
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 35_1 完整处理成功
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 36_1 完整处理成功
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 37_1 完整处理成功
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 38_1 完整处理成功
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 39_1 完整处理成功
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 3_1 合成和音频添加完成: F:/github/aicut_auto/water_remove\3_1_remove_with_audio.mp4
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 3_1 完整处理成功
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 40_1 合成和音频添加完成: F:/github/aicut_auto/water_remove\40_1_remove_with_audio.mp4
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 40_1 完整处理成功
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 41_1 合成和音频添加完成: F:/github/aicut_auto/water_remove\41_1_remove_with_audio.mp4
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 41_1 完整处理成功
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 4_1 合成和音频添加完成: F:/github/aicut_auto/water_remove\4_1_remove_with_audio.mp4
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 4_1 完整处理成功
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 7_1 合成和音频添加完成: F:/github/aicut_auto/water_remove\7_1_remove_with_audio.mp4
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 5_1 合成和音频添加完成: F:/github/aicut_auto/water_remove\5_1_remove_with_audio.mp4
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 5_1 完整处理成功
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 8_1 合成和音频添加完成: F:/github/aicut_auto/water_remove\8_1_remove_with_audio.mp4
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 6_1 合成和音频添加完成: F:/github/aicut_auto/water_remove\6_1_remove_with_audio.mp4
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 6_1 完整处理成功
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 7_1 完整处理成功
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 8_1 完整处理成功
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 9_1 合成和音频添加完成: F:/github/aicut_auto/water_remove\9_1_remove_with_audio.mp4
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 9_1 完整处理成功
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: === 并行视频合成完成 ===
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 成功合成视频数: 41/41
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 成功添加音频数: 41/41
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: === 合成和音频添加阶段完成 ===
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 成功处理视频数: 41/41
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: === 视频处理流程全部完成 ===
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: LaMa模型资源清理完成
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 程序总执行耗时: 553.62 秒
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 程序总执行耗时: 9 分 13.62 秒
2025-08-15 17:32:49 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:270 - watermark_removal2.py 脚本执行成功
2025-08-15 17:32:49 - aicut_auto.core.watermark_remover - INFO - remove_watermark:85 - 水印去除任务完成
2025-08-15 17:34:15 - aicut_auto.CapCutImporter - INFO - import_to_capcut:36 - 开始CapCut导入任务
2025-08-15 17:34:15 - aicut_auto.CapCutImporter - INFO - _build_command_args:203 - 构建的命令行参数: --fps 30
2025-08-15 17:34:15 - aicut_auto.CapCutImporter - INFO - _execute_script_direct:209 - 开始执行CapCut导入脚本
2025-08-15 17:34:15 - aicut_auto.CapCutImporter - INFO - _execute_script_direct:212 - 正在导入 close_capcut 模块...
2025-08-15 17:34:15 - aicut_auto.CapCutImporter - INFO - _execute_script_direct:225 - 尝试关闭 CapCut 窗口...
2025-08-15 17:34:15 - aicut_auto.CapCutImporter - INFO - _execute_script_direct:229 - CapCut 窗口已关闭
2025-08-15 17:34:15 - aicut_auto.CapCutImporter - INFO - _execute_script_direct:236 - 正在导入 5-1_generate_capcut_json.py...
2025-08-15 17:36:01 - aicut_auto.CapCutImporter - INFO - _execute_script_direct:259 - CapCut JSON文件生成成功: F:\github\aicut_auto\草稿模板\draft_content.json
2025-08-15 17:36:01 - aicut_auto.CapCutImporter - INFO - import_to_capcut:53 - CapCut导入任务完成
2025-08-15 19:53:39 - aicut_auto - INFO - _log_startup:101 - 日志系统已初始化
2025-08-15 19:53:39 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-15 19:53:39 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-15 19:53:39 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-15 19:53:39 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-15 19:53:39 - aicut_auto - INFO - _log_startup:101 - 日志系统已初始化
2025-08-15 19:53:40 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-15 19:53:40 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-15 19:53:40 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-15 19:53:40 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-15 19:53:40 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-15 19:53:40 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-15 19:53:40 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-15 19:53:40 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-15 19:53:40 - aicut_auto - INFO - startup_event:811 - === AI Cut Auto Backend 启动 ===
2025-08-15 19:53:40 - aicut_auto - INFO - startup_event:812 - 项目根目录: F:\github\aicut_auto
2025-08-15 19:53:40 - aicut_auto - INFO - startup_event:814 - 任务管理器已初始化
2025-08-15 19:53:40 - aicut_auto - INFO - startup_event:818 - 全局进程跟踪器已初始化
2025-08-15 19:53:40 - aicut_auto - INFO - startup_event:820 - === 后端服务启动完成 ===
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - __init__:36 - AI视频分割器已启用进程跟踪，task_id: a68ea5c0-d83a-4138-8323-538b3f2c51d2
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - split_video:61 - 开始AI视频分割任务，参数: {'inputVideoPath': 'F:\\github\\aicut_auto\\原视频合并\\cutbefore.mp4', 'outputDirPath': 'F:\\github\\aicut_auto\\ai-video-splitter', 'frameBatchSize': 2000, 'sceneBatchSize': 500, 'videoEncodePreset': 'p4', 'videoBitrate': '5M', 'processMode': 'fast'}
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:129 - AI视频分割开始:
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:130 -   输入视频: F:\github\aicut_auto\原视频合并\cutbefore.mp4
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:131 -   输出目录: F:\github\aicut_auto\ai-video-splitter
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:132 -   帧批量大小: 2000
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:133 -   场景批量大小: 500
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:134 -   视频编码预设: p4
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:135 -   视频码率: 5M
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:136 -   处理模式: fast
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:160 - 使用字幕文件: F:\github\aicut_auto\原视频合并\cutbefore.srt
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - _call_ai_splitter_script:186 - 进度: 10% - 准备调用AI分割脚本...
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:206 - 使用subprocess方式调用AI分割脚本...
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:207 - 进度: 20% - 启动AI视频分割进程...
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:226 - 使用Python解释器: F:\github\aicut_auto\.venv\Scripts\python.exe
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:227 - 执行命令: F:\github\aicut_auto\.venv\Scripts\python.exe F:\github\aicut_auto\2-2-2_ai_video_splitter(全视频场景分割).py F:\github\aicut_auto\原视频合并\cutbefore.mp4 F:\github\aicut_auto\原视频合并\cutbefore.srt F:\github\aicut_auto\ai-video-splitter --frame-batch 2000 --scene-batch 500 --encode-preset p4 --bitrate 5M
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:248 - 已注册AI分割子进程PID: 26432
2025-08-15 19:59:42 - aicut_auto - INFO - shutdown_event:825 - AI Cut Auto Backend 关闭中...
2025-08-15 19:59:42 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:345 - AI分割任务被用户主动停止
2025-08-15 19:59:42 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:346 - 进度: 停止 - AI视频分割已被用户取消
2025-08-15 19:59:42 - aicut_auto - INFO - shutdown_event:837 - AI Cut Auto Backend 已关闭
2025-08-15 19:59:42 - aicut_auto.core.ai_video_splitter - INFO - _count_generated_files:404 - 生成了 585 个场景文件
2025-08-15 20:18:47 - aicut_auto - INFO - _log_startup:101 - 日志系统已初始化
2025-08-15 20:18:47 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-15 20:18:47 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-15 20:18:47 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-15 20:18:47 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-15 20:18:47 - aicut_auto - INFO - _log_startup:101 - 日志系统已初始化
2025-08-15 20:18:47 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-15 20:18:47 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-15 20:18:47 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-15 20:18:47 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-15 20:18:47 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-15 20:18:47 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-15 20:18:47 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-15 20:18:47 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-15 20:18:47 - aicut_auto - INFO - startup_event:811 - === AI Cut Auto Backend 启动 ===
2025-08-15 20:18:47 - aicut_auto - INFO - startup_event:812 - 项目根目录: F:\github\aicut_auto
2025-08-15 20:18:47 - aicut_auto - INFO - startup_event:814 - 任务管理器已初始化
2025-08-15 20:18:47 - aicut_auto - INFO - startup_event:818 - 全局进程跟踪器已初始化
2025-08-15 20:18:47 - aicut_auto - INFO - startup_event:820 - === 后端服务启动完成 ===
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - __init__:36 - AI视频分割器已启用进程跟踪，task_id: 21b00ee4-2033-49f7-8887-2ff4eb96be1e
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - split_video:61 - 开始AI视频分割任务，参数: {'inputVideoPath': 'F:\\github\\aicut_auto\\原视频合并\\cutbefore.mp4', 'outputDirPath': 'F:\\github\\aicut_auto\\ai-video-splitter', 'frameBatchSize': 2000, 'sceneBatchSize': 500, 'videoEncodePreset': 'p4', 'videoBitrate': '5M', 'processMode': 'fast'}
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:129 - AI视频分割开始:
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:130 -   输入视频: F:\github\aicut_auto\原视频合并\cutbefore.mp4
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:131 -   输出目录: F:\github\aicut_auto\ai-video-splitter
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:132 -   帧批量大小: 2000
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:133 -   场景批量大小: 500
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:134 -   视频编码预设: p4
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:135 -   视频码率: 5M
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:136 -   处理模式: fast
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:160 - 使用字幕文件: F:\github\aicut_auto\原视频合并\cutbefore.srt
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - _call_ai_splitter_script:186 - 进度: 10% - 准备调用AI分割脚本...
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:206 - 使用subprocess方式调用AI分割脚本...
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:207 - 进度: 20% - 启动AI视频分割进程...
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:226 - 使用Python解释器: F:\github\aicut_auto\.venv\Scripts\python.exe
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:227 - 执行命令: F:\github\aicut_auto\.venv\Scripts\python.exe F:\github\aicut_auto\2-2-2_ai_video_splitter(全视频场景分割).py F:\github\aicut_auto\原视频合并\cutbefore.mp4 F:\github\aicut_auto\原视频合并\cutbefore.srt F:\github\aicut_auto\ai-video-splitter --frame-batch 2000 --scene-batch 500 --encode-preset p4 --bitrate 5M
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:248 - 已注册AI分割子进程PID: 79692
2025-08-15 20:23:36 - aicut_auto - INFO - shutdown_event:825 - AI Cut Auto Backend 关闭中...
2025-08-15 20:23:36 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:345 - AI分割任务被用户主动停止
2025-08-15 20:23:36 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:346 - 进度: 停止 - AI视频分割已被用户取消
2025-08-15 20:23:36 - aicut_auto - INFO - shutdown_event:837 - AI Cut Auto Backend 已关闭
2025-08-15 20:23:36 - aicut_auto.core.ai_video_splitter - INFO - _count_generated_files:404 - 生成了 585 个场景文件
2025-08-15 20:23:42 - aicut_auto - INFO - _log_startup:101 - 日志系统已初始化
2025-08-15 20:23:42 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-15 20:23:42 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-15 20:23:42 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-15 20:23:42 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-15 20:23:42 - aicut_auto - INFO - _log_startup:101 - 日志系统已初始化
2025-08-15 20:23:43 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-15 20:23:43 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-15 20:23:43 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-15 20:23:43 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-15 20:23:43 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-15 20:23:43 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-15 20:23:43 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-15 20:23:43 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-15 20:23:43 - aicut_auto - INFO - startup_event:811 - === AI Cut Auto Backend 启动 ===
2025-08-15 20:23:43 - aicut_auto - INFO - startup_event:812 - 项目根目录: F:\github\aicut_auto
2025-08-15 20:23:43 - aicut_auto - INFO - startup_event:814 - 任务管理器已初始化
2025-08-15 20:23:43 - aicut_auto - INFO - startup_event:818 - 全局进程跟踪器已初始化
2025-08-15 20:23:43 - aicut_auto - INFO - startup_event:820 - === 后端服务启动完成 ===
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - __init__:36 - AI视频分割器已启用进程跟踪，task_id: d030fcb6-1e11-4efc-ad3a-a3856b109663
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - split_video:61 - 开始AI视频分割任务，参数: {'inputVideoPath': 'F:\\github\\aicut_auto\\原视频合并\\cutbefore.mp4', 'outputDirPath': 'F:\\github\\aicut_auto\\ai-video-splitter', 'frameBatchSize': 2000, 'sceneBatchSize': 500, 'videoEncodePreset': 'p4', 'videoBitrate': '5M', 'processMode': 'fast'}
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:129 - AI视频分割开始:
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:130 -   输入视频: F:\github\aicut_auto\原视频合并\cutbefore.mp4
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:131 -   输出目录: F:\github\aicut_auto\ai-video-splitter
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:132 -   帧批量大小: 2000
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:133 -   场景批量大小: 500
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:134 -   视频编码预设: p4
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:135 -   视频码率: 5M
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:136 -   处理模式: fast
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:160 - 使用字幕文件: F:\github\aicut_auto\原视频合并\cutbefore.srt
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - _call_ai_splitter_script:186 - 进度: 10% - 准备调用AI分割脚本...
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:206 - 使用subprocess方式调用AI分割脚本...
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:207 - 进度: 20% - 启动AI视频分割进程...
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:226 - 使用Python解释器: F:\github\aicut_auto\.venv\Scripts\python.exe
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:227 - 执行命令: F:\github\aicut_auto\.venv\Scripts\python.exe F:\github\aicut_auto\2-2-2_ai_video_splitter(全视频场景分割).py F:\github\aicut_auto\原视频合并\cutbefore.mp4 F:\github\aicut_auto\原视频合并\cutbefore.srt F:\github\aicut_auto\ai-video-splitter --frame-batch 2000 --scene-batch 500 --encode-preset p4 --bitrate 5M
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:248 - 已注册AI分割子进程PID: 78884
2025-08-15 20:29:22 - aicut_auto - INFO - shutdown_event:825 - AI Cut Auto Backend 关闭中...
2025-08-15 20:29:22 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:345 - AI分割任务被用户主动停止
2025-08-15 20:29:22 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:346 - 进度: 停止 - AI视频分割已被用户取消
2025-08-15 20:29:22 - aicut_auto - INFO - shutdown_event:837 - AI Cut Auto Backend 已关闭
2025-08-15 20:29:22 - aicut_auto.core.ai_video_splitter - INFO - _count_generated_files:404 - 生成了 0 个场景文件
2025-08-15 20:33:56 - aicut_auto - INFO - _log_startup:101 - 日志系统已初始化
2025-08-15 20:33:56 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-15 20:33:56 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-15 20:33:56 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-15 20:33:56 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-15 20:33:56 - aicut_auto - INFO - _log_startup:101 - 日志系统已初始化
2025-08-15 20:33:56 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-15 20:33:56 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-15 20:33:56 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-15 20:33:56 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-15 20:33:56 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-15 20:33:56 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-15 20:33:56 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-15 20:33:56 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-15 20:33:56 - aicut_auto - INFO - startup_event:811 - === AI Cut Auto Backend 启动 ===
2025-08-15 20:33:56 - aicut_auto - INFO - startup_event:812 - 项目根目录: F:\github\aicut_auto
2025-08-15 20:33:56 - aicut_auto - INFO - startup_event:814 - 任务管理器已初始化
2025-08-15 20:33:56 - aicut_auto - INFO - startup_event:818 - 全局进程跟踪器已初始化
2025-08-15 20:33:56 - aicut_auto - INFO - startup_event:820 - === 后端服务启动完成 ===
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - __init__:36 - AI视频分割器已启用进程跟踪，task_id: c508aab0-8061-4015-8e2e-de4b391e833c
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - split_video:61 - 开始AI视频分割任务，参数: {'inputVideoPath': 'F:\\github\\aicut_auto\\原视频合并\\cutbefore.mp4', 'outputDirPath': 'F:\\github\\aicut_auto\\ai-video-splitter', 'frameBatchSize': 2000, 'sceneBatchSize': 500, 'videoEncodePreset': 'p4', 'videoBitrate': '5M', 'processMode': 'fast'}
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:129 - AI视频分割开始:
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:130 -   输入视频: F:\github\aicut_auto\原视频合并\cutbefore.mp4
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:131 -   输出目录: F:\github\aicut_auto\ai-video-splitter
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:132 -   帧批量大小: 2000
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:133 -   场景批量大小: 500
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:134 -   视频编码预设: p4
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:135 -   视频码率: 5M
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:136 -   处理模式: fast
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:160 - 使用字幕文件: F:\github\aicut_auto\原视频合并\cutbefore.srt
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - _call_ai_splitter_script:186 - 进度: 10% - 准备调用AI分割脚本...
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:206 - 使用subprocess方式调用AI分割脚本...
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:207 - 进度: 20% - 启动AI视频分割进程...
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:226 - 使用Python解释器: F:\github\aicut_auto\.venv\Scripts\python.exe
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:227 - 执行命令: F:\github\aicut_auto\.venv\Scripts\python.exe F:\github\aicut_auto\2-2-2_ai_video_splitter(全视频场景分割).py F:\github\aicut_auto\原视频合并\cutbefore.mp4 F:\github\aicut_auto\原视频合并\cutbefore.srt F:\github\aicut_auto\ai-video-splitter --frame-batch 2000 --scene-batch 500 --encode-preset p4 --bitrate 5M
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:248 - 已注册AI分割子进程PID: 73620
2025-08-15 20:50:46 - aicut_auto - INFO - shutdown_event:825 - AI Cut Auto Backend 关闭中...
2025-08-15 20:50:46 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:345 - AI分割任务被用户主动停止
2025-08-15 20:50:46 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:346 - 进度: 停止 - AI视频分割已被用户取消
2025-08-15 20:50:46 - aicut_auto - INFO - shutdown_event:837 - AI Cut Auto Backend 已关闭
2025-08-15 20:50:46 - aicut_auto.core.ai_video_splitter - INFO - _count_generated_files:404 - 生成了 0 个场景文件
2025-08-16 08:40:14 - aicut_auto - INFO - _log_startup:101 - 日志系统已初始化
2025-08-16 08:40:14 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-16 08:40:14 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-16 08:40:14 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-16 08:40:14 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-16 08:40:14 - aicut_auto - INFO - _log_startup:101 - 日志系统已初始化
2025-08-16 08:40:15 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-16 08:40:15 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-16 08:40:15 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-16 08:40:15 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-16 08:40:15 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-16 08:40:15 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-16 08:40:15 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-16 08:40:15 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-16 08:40:15 - aicut_auto - INFO - startup_event:811 - === AI Cut Auto Backend 启动 ===
2025-08-16 08:40:15 - aicut_auto - INFO - startup_event:812 - 项目根目录: F:\github\aicut_auto
2025-08-16 08:40:15 - aicut_auto - INFO - startup_event:814 - 任务管理器已初始化
2025-08-16 08:40:15 - aicut_auto - INFO - startup_event:818 - 全局进程跟踪器已初始化
2025-08-16 08:40:15 - aicut_auto - INFO - startup_event:820 - === 后端服务启动完成 ===
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - __init__:36 - AI视频分割器已启用进程跟踪，task_id: 1d1152a6-50c1-4050-8a63-471818e4959c
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - split_video:61 - 开始AI视频分割任务，参数: {'inputVideoPath': 'F:\\github\\aicut_auto\\原视频合并\\cutbefore.mp4', 'outputDirPath': 'F:\\github\\aicut_auto\\ai-video-splitter', 'frameBatchSize': 2000, 'sceneBatchSize': 500, 'videoEncodePreset': 'p4', 'videoBitrate': '5M', 'processMode': 'fast'}
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:129 - AI视频分割开始:
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:130 -   输入视频: F:\github\aicut_auto\原视频合并\cutbefore.mp4
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:131 -   输出目录: F:\github\aicut_auto\ai-video-splitter
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:132 -   帧批量大小: 2000
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:133 -   场景批量大小: 500
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:134 -   视频编码预设: p4
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:135 -   视频码率: 5M
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:136 -   处理模式: fast
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:160 - 使用字幕文件: F:\github\aicut_auto\原视频合并\cutbefore.srt
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - _call_ai_splitter_script:186 - 进度: 10% - 准备调用AI分割脚本...
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:206 - 使用subprocess方式调用AI分割脚本...
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:207 - 进度: 20% - 启动AI视频分割进程...
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:226 - 使用Python解释器: F:\github\aicut_auto\.venv\Scripts\python.exe
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:227 - 执行命令: F:\github\aicut_auto\.venv\Scripts\python.exe F:\github\aicut_auto\2-2-2_ai_video_splitter(全视频场景分割).py F:\github\aicut_auto\原视频合并\cutbefore.mp4 F:\github\aicut_auto\原视频合并\cutbefore.srt F:\github\aicut_auto\ai-video-splitter --frame-batch 2000 --scene-batch 500 --encode-preset p4 --bitrate 5M
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:248 - 已注册AI分割子进程PID: 81400
2025-08-16 08:47:29 - aicut_auto - INFO - shutdown_event:825 - AI Cut Auto Backend 关闭中...
2025-08-16 08:47:29 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:345 - AI分割任务被用户主动停止
2025-08-16 08:47:29 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:346 - 进度: 停止 - AI视频分割已被用户取消
2025-08-16 08:47:29 - aicut_auto - INFO - shutdown_event:837 - AI Cut Auto Backend 已关闭
2025-08-16 08:47:29 - aicut_auto.core.ai_video_splitter - INFO - _count_generated_files:404 - 生成了 0 个场景文件
2025-08-16 08:47:35 - aicut_auto - INFO - _log_startup:101 - 日志系统已初始化
2025-08-16 08:47:35 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-16 08:47:35 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-16 08:47:35 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-16 08:47:35 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-16 08:47:36 - aicut_auto - INFO - _log_startup:101 - 日志系统已初始化
2025-08-16 08:47:36 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-16 08:47:36 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-16 08:47:36 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-16 08:47:36 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-16 08:47:36 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-16 08:47:36 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-16 08:47:36 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-16 08:47:36 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-16 08:47:36 - aicut_auto - INFO - startup_event:811 - === AI Cut Auto Backend 启动 ===
2025-08-16 08:47:36 - aicut_auto - INFO - startup_event:812 - 项目根目录: F:\github\aicut_auto
2025-08-16 08:47:36 - aicut_auto - INFO - startup_event:814 - 任务管理器已初始化
2025-08-16 08:47:36 - aicut_auto - INFO - startup_event:818 - 全局进程跟踪器已初始化
2025-08-16 08:47:36 - aicut_auto - INFO - startup_event:820 - === 后端服务启动完成 ===
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - __init__:36 - AI视频分割器已启用进程跟踪，task_id: 64f8954a-720e-4ec4-bf11-fdfdb6da86f8
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - split_video:61 - 开始AI视频分割任务，参数: {'inputVideoPath': 'F:\\github\\aicut_auto\\原视频合并\\cutbefore.mp4', 'outputDirPath': 'F:\\github\\aicut_auto\\ai-video-splitter', 'frameBatchSize': 2000, 'sceneBatchSize': 500, 'videoEncodePreset': 'p4', 'videoBitrate': '5M', 'processMode': 'fast'}
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:129 - AI视频分割开始:
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:130 -   输入视频: F:\github\aicut_auto\原视频合并\cutbefore.mp4
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:131 -   输出目录: F:\github\aicut_auto\ai-video-splitter
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:132 -   帧批量大小: 2000
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:133 -   场景批量大小: 500
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:134 -   视频编码预设: p4
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:135 -   视频码率: 5M
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:136 -   处理模式: fast
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:160 - 使用字幕文件: F:\github\aicut_auto\原视频合并\cutbefore.srt
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - _call_ai_splitter_script:186 - 进度: 10% - 准备调用AI分割脚本...
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:206 - 使用subprocess方式调用AI分割脚本...
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:207 - 进度: 20% - 启动AI视频分割进程...
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:226 - 使用Python解释器: F:\github\aicut_auto\.venv\Scripts\python.exe
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:227 - 执行命令: F:\github\aicut_auto\.venv\Scripts\python.exe F:\github\aicut_auto\2-2-2_ai_video_splitter(全视频场景分割).py F:\github\aicut_auto\原视频合并\cutbefore.mp4 F:\github\aicut_auto\原视频合并\cutbefore.srt F:\github\aicut_auto\ai-video-splitter --frame-batch 2000 --scene-batch 500 --encode-preset p4 --bitrate 5M
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:248 - 已注册AI分割子进程PID: 78968
2025-08-16 09:15:50 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:332 - 进度: 100% - AI视频分割完成
2025-08-16 09:15:50 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:333 - AI分割脚本执行成功
2025-08-16 09:15:50 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:336 - 脚本stderr输出:
2025-08-16 09:15:50 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   2025-08-16 09:06:34.584235: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-08-16 09:15:50 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   2025-08-16 09:06:35.184563: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-08-16 09:15:50 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   2025-08-16 09:06:39.555464: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.
2025-08-16 09:15:50 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   To enable the following instructions: SSE3 SSE4.1 SSE4.2 AVX AVX2 AVX_VNNI FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.
2025-08-16 09:15:50 - aicut_auto.core.ai_video_splitter - INFO - _count_generated_files:404 - 生成了 1729 个场景文件
2025-08-16 09:15:50 - aicut_auto.core.ai_video_splitter - INFO - split_video:92 - AI视频分割任务完成
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - __init__:36 - AI视频分割器已启用进程跟踪，task_id: c2bcb9bb-d124-4d44-a2dc-6e842eacc64b
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - split_video:61 - 开始AI视频分割任务，参数: {'inputVideoPath': 'F:\\github\\aicut_auto\\原视频合并\\cutbefore.mp4', 'outputDirPath': 'F:\\github\\aicut_auto\\ai-video-splitter', 'frameBatchSize': 2000, 'sceneBatchSize': 500, 'videoEncodePreset': 'p4', 'videoBitrate': '5M', 'processMode': 'fast'}
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:129 - AI视频分割开始:
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:130 -   输入视频: F:\github\aicut_auto\原视频合并\cutbefore.mp4
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:131 -   输出目录: F:\github\aicut_auto\ai-video-splitter
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:132 -   帧批量大小: 2000
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:133 -   场景批量大小: 500
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:134 -   视频编码预设: p4
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:135 -   视频码率: 5M
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:136 -   处理模式: fast
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:160 - 使用字幕文件: F:\github\aicut_auto\原视频合并\cutbefore.srt
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - _call_ai_splitter_script:186 - 进度: 10% - 准备调用AI分割脚本...
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:206 - 使用subprocess方式调用AI分割脚本...
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:207 - 进度: 20% - 启动AI视频分割进程...
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:226 - 使用Python解释器: F:\github\aicut_auto\.venv\Scripts\python.exe
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:227 - 执行命令: F:\github\aicut_auto\.venv\Scripts\python.exe F:\github\aicut_auto\2-2-2_ai_video_splitter(全视频场景分割).py F:\github\aicut_auto\原视频合并\cutbefore.mp4 F:\github\aicut_auto\原视频合并\cutbefore.srt F:\github\aicut_auto\ai-video-splitter --frame-batch 2000 --scene-batch 500 --encode-preset p4 --bitrate 5M
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:248 - 已注册AI分割子进程PID: 80780
2025-08-16 10:32:23 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:332 - 进度: 100% - AI视频分割完成
2025-08-16 10:32:23 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:333 - AI分割脚本执行成功
2025-08-16 10:32:23 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:336 - 脚本stderr输出:
2025-08-16 10:32:23 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   2025-08-16 10:22:19.242723: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-08-16 10:32:23 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   2025-08-16 10:22:19.846816: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-08-16 10:32:23 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   2025-08-16 10:22:24.413507: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.
2025-08-16 10:32:23 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   To enable the following instructions: SSE3 SSE4.1 SSE4.2 AVX AVX2 AVX_VNNI FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.
2025-08-16 10:32:23 - aicut_auto.core.ai_video_splitter - INFO - _count_generated_files:404 - 生成了 1729 个场景文件
2025-08-16 10:32:23 - aicut_auto.core.ai_video_splitter - INFO - split_video:92 - AI视频分割任务完成
2025-08-16 11:00:11 - aicut_auto - INFO - _log_startup:101 - 日志系统已初始化
2025-08-16 11:00:11 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-16 11:00:11 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-16 11:00:11 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-16 11:00:11 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-16 11:00:12 - aicut_auto - INFO - _log_startup:101 - 日志系统已初始化
2025-08-16 11:00:12 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-16 11:00:12 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-16 11:00:12 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-16 11:00:12 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-16 11:00:12 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-16 11:00:12 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-16 11:00:12 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-16 11:00:12 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-16 11:00:12 - aicut_auto - INFO - startup_event:811 - === AI Cut Auto Backend 启动 ===
2025-08-16 11:00:12 - aicut_auto - INFO - startup_event:812 - 项目根目录: F:\github\aicut_auto
2025-08-16 11:00:12 - aicut_auto - INFO - startup_event:814 - 任务管理器已初始化
2025-08-16 11:00:12 - aicut_auto - INFO - startup_event:818 - 全局进程跟踪器已初始化
2025-08-16 11:00:12 - aicut_auto - INFO - startup_event:820 - === 后端服务启动完成 ===
2025-08-16 11:02:26 - aicut_auto.core.ai_video_splitter - INFO - __init__:36 - AI视频分割器已启用进程跟踪，task_id: 4175f65c-3023-43cf-9940-85d3ece7e26d
2025-08-16 11:02:26 - aicut_auto.core.ai_video_splitter - INFO - split_video:61 - 开始AI视频分割任务，参数: {'inputVideoPath': 'F:\\github\\aicut_auto\\原视频合并\\cutbefore.mp4', 'outputDirPath': 'F:\\github\\aicut_auto\\ai-video-splitter', 'frameBatchSize': 2000, 'sceneBatchSize': 500, 'videoEncodePreset': 'p4', 'videoBitrate': '5M', 'processMode': 'fast'}
2025-08-16 11:02:26 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:129 - AI视频分割开始:
2025-08-16 11:02:26 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:130 -   输入视频: F:\github\aicut_auto\原视频合并\cutbefore.mp4
2025-08-16 11:02:26 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:131 -   输出目录: F:\github\aicut_auto\ai-video-splitter
2025-08-16 11:02:26 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:132 -   帧批量大小: 2000
2025-08-16 11:02:26 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:133 -   场景批量大小: 500
2025-08-16 11:02:26 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:134 -   视频编码预设: p4
2025-08-16 11:02:26 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:135 -   视频码率: 5M
2025-08-16 11:02:26 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:136 -   处理模式: fast
2025-08-16 11:02:26 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:160 - 使用字幕文件: F:\github\aicut_auto\原视频合并\cutbefore.srt
2025-08-16 11:02:26 - aicut_auto.core.ai_video_splitter - INFO - _call_ai_splitter_script:186 - 进度: 10% - 准备调用AI分割脚本...
2025-08-16 11:02:26 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:206 - 使用subprocess方式调用AI分割脚本...
2025-08-16 11:02:26 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:207 - 进度: 20% - 启动AI视频分割进程...
2025-08-16 11:02:26 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:226 - 使用Python解释器: F:\github\aicut_auto\.venv\Scripts\python.exe
2025-08-16 11:02:26 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:227 - 执行命令: F:\github\aicut_auto\.venv\Scripts\python.exe F:\github\aicut_auto\2-2-2_ai_video_splitter(全视频场景分割).py F:\github\aicut_auto\原视频合并\cutbefore.mp4 F:\github\aicut_auto\原视频合并\cutbefore.srt F:\github\aicut_auto\ai-video-splitter --frame-batch 2000 --scene-batch 500 --encode-preset p4 --bitrate 5M
2025-08-16 11:02:26 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:248 - 已注册AI分割子进程PID: 43356
2025-08-16 11:12:21 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:332 - 进度: 100% - AI视频分割完成
2025-08-16 11:12:21 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:333 - AI分割脚本执行成功
2025-08-16 11:12:21 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:336 - 脚本stderr输出:
2025-08-16 11:12:21 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   2025-08-16 11:02:27.995039: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-08-16 11:12:21 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   2025-08-16 11:02:28.612423: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-08-16 11:12:21 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   2025-08-16 11:02:32.992745: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.
2025-08-16 11:12:21 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   To enable the following instructions: SSE3 SSE4.1 SSE4.2 AVX AVX2 AVX_VNNI FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.
2025-08-16 11:12:21 - aicut_auto.core.ai_video_splitter - INFO - _count_generated_files:404 - 生成了 1729 个场景文件
2025-08-16 11:12:21 - aicut_auto.core.ai_video_splitter - INFO - split_video:92 - AI视频分割任务完成
2025-08-16 11:17:08 - aicut_auto.core.ai_video_splitter - INFO - __init__:36 - AI视频分割器已启用进程跟踪，task_id: 7be83365-9f34-4266-bc1c-426114a5f735
2025-08-16 11:17:08 - aicut_auto.core.ai_video_splitter - INFO - split_video:61 - 开始AI视频分割任务，参数: {'inputVideoPath': 'F:\\github\\aicut_auto\\原视频合并\\cutbefore.mp4', 'outputDirPath': 'F:\\github\\aicut_auto\\ai-video-splitter', 'frameBatchSize': 2000, 'sceneBatchSize': 500, 'videoEncodePreset': 'p4', 'videoBitrate': '5M', 'processMode': 'fast'}
2025-08-16 11:17:08 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:129 - AI视频分割开始:
2025-08-16 11:17:08 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:130 -   输入视频: F:\github\aicut_auto\原视频合并\cutbefore.mp4
2025-08-16 11:17:08 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:131 -   输出目录: F:\github\aicut_auto\ai-video-splitter
2025-08-16 11:17:08 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:132 -   帧批量大小: 2000
2025-08-16 11:17:08 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:133 -   场景批量大小: 500
2025-08-16 11:17:08 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:134 -   视频编码预设: p4
2025-08-16 11:17:08 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:135 -   视频码率: 5M
2025-08-16 11:17:08 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:136 -   处理模式: fast
2025-08-16 11:17:08 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:160 - 使用字幕文件: F:\github\aicut_auto\原视频合并\cutbefore.srt
2025-08-16 11:17:08 - aicut_auto.core.ai_video_splitter - INFO - _call_ai_splitter_script:186 - 进度: 10% - 准备调用AI分割脚本...
2025-08-16 11:17:08 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:206 - 使用subprocess方式调用AI分割脚本...
2025-08-16 11:17:08 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:207 - 进度: 20% - 启动AI视频分割进程...
2025-08-16 11:17:08 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:226 - 使用Python解释器: F:\github\aicut_auto\.venv\Scripts\python.exe
2025-08-16 11:17:08 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:227 - 执行命令: F:\github\aicut_auto\.venv\Scripts\python.exe F:\github\aicut_auto\2-2-2_ai_video_splitter(全视频场景分割).py F:\github\aicut_auto\原视频合并\cutbefore.mp4 F:\github\aicut_auto\原视频合并\cutbefore.srt F:\github\aicut_auto\ai-video-splitter --frame-batch 2000 --scene-batch 500 --encode-preset p4 --bitrate 5M
2025-08-16 11:17:08 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:248 - 已注册AI分割子进程PID: 88368
2025-08-16 11:18:31 - aicut_auto - INFO - shutdown_event:825 - AI Cut Auto Backend 关闭中...
2025-08-16 11:18:31 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:345 - AI分割任务被用户主动停止
2025-08-16 11:18:31 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:346 - 进度: 停止 - AI视频分割已被用户取消
2025-08-16 11:18:31 - aicut_auto - INFO - shutdown_event:837 - AI Cut Auto Backend 已关闭
2025-08-16 11:18:31 - aicut_auto.core.ai_video_splitter - INFO - _count_generated_files:404 - 生成了 0 个场景文件
2025-08-16 11:18:44 - aicut_auto - INFO - _log_startup:101 - 日志系统已初始化
2025-08-16 11:18:44 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-16 11:18:44 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-16 11:18:44 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-16 11:18:44 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-16 11:18:45 - aicut_auto - INFO - _log_startup:101 - 日志系统已初始化
2025-08-16 11:18:45 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-16 11:18:45 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-16 11:18:45 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-16 11:18:45 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-16 11:18:45 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-16 11:18:45 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-16 11:18:45 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-16 11:18:45 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-16 11:18:45 - aicut_auto - INFO - startup_event:811 - === AI Cut Auto Backend 启动 ===
2025-08-16 11:18:45 - aicut_auto - INFO - startup_event:812 - 项目根目录: F:\github\aicut_auto
2025-08-16 11:18:45 - aicut_auto - INFO - startup_event:814 - 任务管理器已初始化
2025-08-16 11:18:45 - aicut_auto - INFO - startup_event:818 - 全局进程跟踪器已初始化
2025-08-16 11:18:45 - aicut_auto - INFO - startup_event:820 - === 后端服务启动完成 ===
2025-08-16 11:18:51 - aicut_auto.core.ai_video_splitter - INFO - __init__:36 - AI视频分割器已启用进程跟踪，task_id: 138c3e08-7aae-4827-aff3-13d9b32b93fe
2025-08-16 11:18:51 - aicut_auto.core.ai_video_splitter - INFO - split_video:61 - 开始AI视频分割任务，参数: {'inputVideoPath': 'F:\\github\\aicut_auto\\原视频合并\\cutbefore.mp4', 'outputDirPath': 'F:\\github\\aicut_auto\\ai-video-splitter', 'frameBatchSize': 2000, 'sceneBatchSize': 500, 'videoEncodePreset': 'p4', 'videoBitrate': '5M', 'processMode': 'fast'}
2025-08-16 11:18:51 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:129 - AI视频分割开始:
2025-08-16 11:18:51 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:130 -   输入视频: F:\github\aicut_auto\原视频合并\cutbefore.mp4
2025-08-16 11:18:51 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:131 -   输出目录: F:\github\aicut_auto\ai-video-splitter
2025-08-16 11:18:51 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:132 -   帧批量大小: 2000
2025-08-16 11:18:51 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:133 -   场景批量大小: 500
2025-08-16 11:18:51 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:134 -   视频编码预设: p4
2025-08-16 11:18:51 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:135 -   视频码率: 5M
2025-08-16 11:18:51 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:136 -   处理模式: fast
2025-08-16 11:18:51 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:160 - 使用字幕文件: F:\github\aicut_auto\原视频合并\cutbefore.srt
2025-08-16 11:18:51 - aicut_auto.core.ai_video_splitter - INFO - _call_ai_splitter_script:186 - 进度: 10% - 准备调用AI分割脚本...
2025-08-16 11:18:51 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:206 - 使用subprocess方式调用AI分割脚本...
2025-08-16 11:18:51 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:207 - 进度: 20% - 启动AI视频分割进程...
2025-08-16 11:18:51 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:226 - 使用Python解释器: F:\github\aicut_auto\.venv\Scripts\python.exe
2025-08-16 11:18:51 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:227 - 执行命令: F:\github\aicut_auto\.venv\Scripts\python.exe F:\github\aicut_auto\2-2-2_ai_video_splitter(全视频场景分割).py F:\github\aicut_auto\原视频合并\cutbefore.mp4 F:\github\aicut_auto\原视频合并\cutbefore.srt F:\github\aicut_auto\ai-video-splitter --frame-batch 2000 --scene-batch 500 --encode-preset p4 --bitrate 5M
2025-08-16 11:18:51 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:248 - 已注册AI分割子进程PID: 48472
2025-08-16 11:27:30 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:332 - 进度: 100% - AI视频分割完成
2025-08-16 11:27:30 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:333 - AI分割脚本执行成功
2025-08-16 11:27:30 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:336 - 脚本stderr输出:
2025-08-16 11:27:30 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   2025-08-16 11:18:52.622715: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-08-16 11:27:30 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   2025-08-16 11:18:53.215194: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-08-16 11:27:30 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   2025-08-16 11:18:57.500814: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.
2025-08-16 11:27:30 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   To enable the following instructions: SSE3 SSE4.1 SSE4.2 AVX AVX2 AVX_VNNI FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.
2025-08-16 11:27:30 - aicut_auto.core.ai_video_splitter - INFO - _count_generated_files:404 - 生成了 1729 个场景文件
2025-08-16 11:27:30 - aicut_auto.core.ai_video_splitter - INFO - split_video:92 - AI视频分割任务完成
2025-08-16 13:59:03 - aicut_auto.core.file_backup - INFO - backup_files:43 - 开始文件备份任务，参数: {'items': [{'type': 'file', 'path': 'F:/github/aicut_auto/en.srt'}, {'type': 'file', 'path': 'F:/github/aicut_auto/yuan.srt'}, {'type': 'file', 'path': 'F:/github/aicut_auto/剧情简介.txt'}, {'type': 'file', 'path': 'F:/github/aicut_auto/cutbefore.srt'}, {'type': 'file', 'path': 'F:/github/aicut_auto/cn_new.srt'}, {'type': 'file', 'path': 'F:/github/aicut_auto/en_new.srt'}, {'type': 'dir', 'path': 'F:/github/aicut_auto/output'}, {'type': 'dir', 'path': 'F:/github/aicut_auto/newcut'}, {'type': 'dir', 'path': 'F:/github/aicut_auto/原视频合并'}, {'type': 'dir', 'path': 'F:/github/aicut_auto/newcut_ai'}], 'target_dir': 'Z:/短剧内容', 'enable_compress': False, 'compress_format': 'zip'}
2025-08-16 13:59:03 - aicut_auto.core.file_backup - INFO - _backup_files_sync:73 - 正在导入 close_capcut 模块...
2025-08-16 13:59:04 - aicut_auto.core.file_backup - INFO - _backup_files_sync:86 - 正在导入 6-file_copy.py...
2025-08-16 13:59:04 - aicut_auto.core.file_backup - INFO - _backup_files_sync:109 - 备份参数: items=[{'type': 'file', 'path': 'F:/github/aicut_auto/en.srt'}, {'type': 'file', 'path': 'F:/github/aicut_auto/yuan.srt'}, {'type': 'file', 'path': 'F:/github/aicut_auto/剧情简介.txt'}, {'type': 'file', 'path': 'F:/github/aicut_auto/cutbefore.srt'}, {'type': 'file', 'path': 'F:/github/aicut_auto/cn_new.srt'}, {'type': 'file', 'path': 'F:/github/aicut_auto/en_new.srt'}, {'type': 'dir', 'path': 'F:/github/aicut_auto/output'}, {'type': 'dir', 'path': 'F:/github/aicut_auto/newcut'}, {'type': 'dir', 'path': 'F:/github/aicut_auto/原视频合并'}, {'type': 'dir', 'path': 'F:/github/aicut_auto/newcut_ai'}], target_dir=Z:/短剧内容\2025年08月16日135904, enable_compress=False
2025-08-16 13:59:04 - aicut_auto.core.file_backup - INFO - _backup_files_sync:113 - 开始关闭 CapCut 窗口...
2025-08-16 13:59:04 - aicut_auto.core.file_backup - INFO - _backup_files_sync:119 - CapCut 窗口关闭失败或未运行，继续执行...
2025-08-16 13:59:04 - aicut_auto.core.file_backup - INFO - _backup_files_sync:124 - 开始关闭望言OCR窗口...
2025-08-16 13:59:04 - aicut_auto.core.file_backup - INFO - _backup_files_sync:130 - 望言OCR窗口关闭失败或未运行，继续执行...
2025-08-16 13:59:04 - aicut_auto.core.file_backup - INFO - _backup_files_sync:137 - 开始复制文件到 Z:/短剧内容\2025年08月16日135904
2025-08-16 13:59:58 - aicut_auto.core.file_backup - INFO - _backup_files_sync:149 - 文件备份成功，开始清空原目录和文件内容...
2025-08-16 14:00:00 - aicut_auto.core.file_backup - INFO - _backup_files_sync:152 - 清空操作完成
2025-08-16 14:00:00 - aicut_auto.core.file_backup - INFO - backup_files:48 - 文件备份任务完成
2025-08-16 14:02:16 - aicut_auto.core.video_merger - INFO - __init__:41 - 为任务 9a3eacac-9dad-4e49-81ea-602fd0a327e6 创建了进程跟踪器
2025-08-16 14:02:16 - aicut_auto - INFO - start_video_merge_task:182 - 视频合并任务已创建，Task ID: 9a3eacac-9dad-4e49-81ea-602fd0a327e6
2025-08-16 14:02:16 - aicut_auto.core.video_merger - INFO - merge_videos_async:64 - 开始视频合并任务，参数: {'inputDir': 'F:\\github\\aicut_auto\\原视频合并', 'outputDir': 'F:\\github\\aicut_auto\\原视频合并', 'outputFile': 'cutbefore.mp4', 'hangerFile': 'hanger.txt', 'lastSeconds': 10, 'trimLastSeconds': 8}
2025-08-16 14:02:16 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:135 - 开始实际合并目录 F:\github\aicut_auto\原视频合并 中的视频文件
2025-08-16 14:02:16 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:136 - 输出文件: F:\github\aicut_auto\原视频合并\cutbefore.mp4
2025-08-16 14:02:16 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:137 - 时间戳文件: F:\github\aicut_auto\原视频合并\hanger.txt
2025-08-16 14:02:16 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:144 - 找到 1 个视频文件
2025-08-16 14:02:16 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   1. 1.mp4
2025-08-16 14:02:16 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:150 - 使用进程跟踪的视频合并模式
2025-08-16 14:02:16 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 77180
2025-08-16 14:02:16 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 54180
2025-08-16 14:02:17 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 85648
2025-08-16 14:02:17 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 59612
2025-08-16 14:02:17 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 75.893696 -i F:\github\aicut_auto\原视频合并\1.mp4...
2025-08-16 14:02:17 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 69548
2025-08-16 14:02:19 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -f concat -safe 0...
2025-08-16 14:02:19 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 87424
2025-08-16 14:02:20 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 58932
2025-08-16 14:02:20 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:157 - 视频合并完成: F:\github\aicut_auto\原视频合并\cutbefore.mp4
2025-08-16 14:02:20 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:158 - 时间戳信息已保存到: F:\github\aicut_auto\原视频合并\hanger.txt
2025-08-16 14:02:20 - aicut_auto.core.video_merger - INFO - merge_videos_async:97 - 视频合并任务完成
2025-08-16 14:04:40 - aicut_auto.core.ai_video_splitter - INFO - __init__:36 - AI视频分割器已启用进程跟踪，task_id: 7a11d8b7-31d0-4173-b9bf-15059e0f41fb
2025-08-16 14:04:40 - aicut_auto.core.ai_video_splitter - INFO - split_video:61 - 开始AI视频分割任务，参数: {'inputVideoPath': 'F:\\github\\aicut_auto\\原视频合并\\cutbefore.mp4', 'outputDirPath': 'F:\\github\\aicut_auto\\ai-video-splitter', 'frameBatchSize': 2000, 'sceneBatchSize': 500, 'videoEncodePreset': 'p4', 'videoBitrate': '5M', 'processMode': 'fast'}
2025-08-16 14:04:40 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:129 - AI视频分割开始:
2025-08-16 14:04:40 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:130 -   输入视频: F:\github\aicut_auto\原视频合并\cutbefore.mp4
2025-08-16 14:04:40 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:131 -   输出目录: F:\github\aicut_auto\ai-video-splitter
2025-08-16 14:04:40 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:132 -   帧批量大小: 2000
2025-08-16 14:04:40 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:133 -   场景批量大小: 500
2025-08-16 14:04:40 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:134 -   视频编码预设: p4
2025-08-16 14:04:40 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:135 -   视频码率: 5M
2025-08-16 14:04:40 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:136 -   处理模式: fast
2025-08-16 14:04:40 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:160 - 使用字幕文件: F:\github\aicut_auto\原视频合并\cutbefore.srt
2025-08-16 14:04:40 - aicut_auto.core.ai_video_splitter - INFO - _call_ai_splitter_script:186 - 进度: 10% - 准备调用AI分割脚本...
2025-08-16 14:04:40 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:206 - 使用subprocess方式调用AI分割脚本...
2025-08-16 14:04:40 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:207 - 进度: 20% - 启动AI视频分割进程...
2025-08-16 14:04:40 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:226 - 使用Python解释器: F:\github\aicut_auto\.venv\Scripts\python.exe
2025-08-16 14:04:40 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:227 - 执行命令: F:\github\aicut_auto\.venv\Scripts\python.exe F:\github\aicut_auto\2-2-2_ai_video_splitter(全视频场景分割).py F:\github\aicut_auto\原视频合并\cutbefore.mp4 F:\github\aicut_auto\原视频合并\cutbefore.srt F:\github\aicut_auto\ai-video-splitter --frame-batch 2000 --scene-batch 500 --encode-preset p4 --bitrate 5M
2025-08-16 14:04:40 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:248 - 已注册AI分割子进程PID: 48304
2025-08-16 14:05:04 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:332 - 进度: 100% - AI视频分割完成
2025-08-16 14:05:04 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:333 - AI分割脚本执行成功
2025-08-16 14:05:04 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:336 - 脚本stderr输出:
2025-08-16 14:05:04 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   2025-08-16 14:04:42.198746: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-08-16 14:05:04 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   2025-08-16 14:04:42.819036: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-08-16 14:05:04 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   2025-08-16 14:04:45.517248: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.
2025-08-16 14:05:04 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   To enable the following instructions: SSE3 SSE4.1 SSE4.2 AVX AVX2 AVX_VNNI FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.
2025-08-16 14:05:04 - aicut_auto.core.ai_video_splitter - INFO - _count_generated_files:404 - 生成了 36 个场景文件
2025-08-16 14:05:04 - aicut_auto.core.ai_video_splitter - INFO - split_video:92 - AI视频分割任务完成
2025-08-16 14:35:01 - aicut_auto.core.ai_video_splitter - INFO - __init__:36 - AI视频分割器已启用进程跟踪，task_id: f7be5e5e-79db-4029-882a-3453f0faf65e
2025-08-16 14:35:01 - aicut_auto.core.ai_video_splitter - INFO - split_video:61 - 开始AI视频分割任务，参数: {'inputVideoPath': 'F:\\github\\aicut_auto\\原视频合并\\cutbefore.mp4', 'outputDirPath': 'F:\\github\\aicut_auto\\ai-video-splitter', 'frameBatchSize': 2000, 'sceneBatchSize': 500, 'videoEncodePreset': 'p4', 'videoBitrate': '5M', 'processMode': 'fast'}
2025-08-16 14:35:01 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:129 - AI视频分割开始:
2025-08-16 14:35:01 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:130 -   输入视频: F:\github\aicut_auto\原视频合并\cutbefore.mp4
2025-08-16 14:35:01 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:131 -   输出目录: F:\github\aicut_auto\ai-video-splitter
2025-08-16 14:35:01 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:132 -   帧批量大小: 2000
2025-08-16 14:35:01 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:133 -   场景批量大小: 500
2025-08-16 14:35:01 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:134 -   视频编码预设: p4
2025-08-16 14:35:01 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:135 -   视频码率: 5M
2025-08-16 14:35:01 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:136 -   处理模式: fast
2025-08-16 14:35:01 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:160 - 使用字幕文件: F:\github\aicut_auto\原视频合并\cutbefore.srt
2025-08-16 14:35:01 - aicut_auto.core.ai_video_splitter - INFO - _call_ai_splitter_script:186 - 进度: 10% - 准备调用AI分割脚本...
2025-08-16 14:35:01 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:206 - 使用subprocess方式调用AI分割脚本...
2025-08-16 14:35:01 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:207 - 进度: 20% - 启动AI视频分割进程...
2025-08-16 14:35:01 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:226 - 使用Python解释器: F:\github\aicut_auto\.venv\Scripts\python.exe
2025-08-16 14:35:01 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:227 - 执行命令: F:\github\aicut_auto\.venv\Scripts\python.exe F:\github\aicut_auto\2-2-2_ai_video_splitter(全视频场景分割).py F:\github\aicut_auto\原视频合并\cutbefore.mp4 F:\github\aicut_auto\原视频合并\cutbefore.srt F:\github\aicut_auto\ai-video-splitter --frame-batch 2000 --scene-batch 500 --encode-preset p4 --bitrate 5M
2025-08-16 14:35:01 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:248 - 已注册AI分割子进程PID: 76976
2025-08-16 14:35:14 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:332 - 进度: 100% - AI视频分割完成
2025-08-16 14:35:14 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:333 - AI分割脚本执行成功
2025-08-16 14:35:14 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:336 - 脚本stderr输出:
2025-08-16 14:35:14 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   Option 4:
2025-08-16 14:35:14 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -     Positional arguments (2 total):
2025-08-16 14:35:14 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -       * TensorSpec(shape=(None, None, 27, 48, 3), dtype=tf.float32, name='input_1')
2025-08-16 14:35:14 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -       * True
2025-08-16 14:35:14 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -     Keyword arguments: {}
2025-08-16 14:35:14 - aicut_auto.core.ai_video_splitter - INFO - _count_generated_files:404 - 生成了 0 个场景文件
2025-08-16 14:35:14 - aicut_auto.core.ai_video_splitter - INFO - split_video:92 - AI视频分割任务完成
2025-08-16 14:36:25 - aicut_auto.core.ai_video_splitter - INFO - __init__:36 - AI视频分割器已启用进程跟踪，task_id: 3e055aa3-499c-4c59-848c-b79d9f574a54
2025-08-16 14:36:25 - aicut_auto.core.ai_video_splitter - INFO - split_video:61 - 开始AI视频分割任务，参数: {'inputVideoPath': 'F:\\github\\aicut_auto\\原视频合并\\cutbefore.mp4', 'outputDirPath': 'F:\\github\\aicut_auto\\ai-video-splitter', 'frameBatchSize': 2000, 'sceneBatchSize': 500, 'videoEncodePreset': 'p4', 'videoBitrate': '5M', 'processMode': 'fast'}
2025-08-16 14:36:25 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:129 - AI视频分割开始:
2025-08-16 14:36:25 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:130 -   输入视频: F:\github\aicut_auto\原视频合并\cutbefore.mp4
2025-08-16 14:36:25 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:131 -   输出目录: F:\github\aicut_auto\ai-video-splitter
2025-08-16 14:36:25 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:132 -   帧批量大小: 2000
2025-08-16 14:36:25 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:133 -   场景批量大小: 500
2025-08-16 14:36:25 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:134 -   视频编码预设: p4
2025-08-16 14:36:25 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:135 -   视频码率: 5M
2025-08-16 14:36:25 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:136 -   处理模式: fast
2025-08-16 14:36:25 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:160 - 使用字幕文件: F:\github\aicut_auto\原视频合并\cutbefore.srt
2025-08-16 14:36:25 - aicut_auto.core.ai_video_splitter - INFO - _call_ai_splitter_script:186 - 进度: 10% - 准备调用AI分割脚本...
2025-08-16 14:36:25 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:206 - 使用subprocess方式调用AI分割脚本...
2025-08-16 14:36:25 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:207 - 进度: 20% - 启动AI视频分割进程...
2025-08-16 14:36:25 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:226 - 使用Python解释器: F:\github\aicut_auto\.venv\Scripts\python.exe
2025-08-16 14:36:25 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:227 - 执行命令: F:\github\aicut_auto\.venv\Scripts\python.exe F:\github\aicut_auto\2-2-2_ai_video_splitter(全视频场景分割).py F:\github\aicut_auto\原视频合并\cutbefore.mp4 F:\github\aicut_auto\原视频合并\cutbefore.srt F:\github\aicut_auto\ai-video-splitter --frame-batch 2000 --scene-batch 500 --encode-preset p4 --bitrate 5M
2025-08-16 14:36:25 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:248 - 已注册AI分割子进程PID: 67388
2025-08-16 14:36:48 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:332 - 进度: 100% - AI视频分割完成
2025-08-16 14:36:48 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:333 - AI分割脚本执行成功
2025-08-16 14:36:48 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:336 - 脚本stderr输出:
2025-08-16 14:36:48 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   2025-08-16 14:36:26.299150: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-08-16 14:36:48 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   2025-08-16 14:36:26.945033: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-08-16 14:36:48 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   2025-08-16 14:36:29.624670: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.
2025-08-16 14:36:48 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   To enable the following instructions: SSE3 SSE4.1 SSE4.2 AVX AVX2 AVX_VNNI FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.
2025-08-16 14:36:48 - aicut_auto.core.ai_video_splitter - INFO - _count_generated_files:404 - 生成了 36 个场景文件
2025-08-16 14:36:48 - aicut_auto.core.ai_video_splitter - INFO - split_video:92 - AI视频分割任务完成
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - __init__:41 - 为任务 9cd1faf4-b613-4ebe-8aa5-4d31aa5395d3 创建了进程跟踪器
2025-08-16 14:51:26 - aicut_auto - INFO - start_video_merge_task:182 - 视频合并任务已创建，Task ID: 9cd1faf4-b613-4ebe-8aa5-4d31aa5395d3
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - merge_videos_async:64 - 开始视频合并任务，参数: {'inputDir': 'F:\\github\\aicut_auto\\原视频合并', 'outputDir': 'F:\\github\\aicut_auto\\原视频合并', 'outputFile': 'cutbefore.mp4', 'hangerFile': 'hanger.txt', 'lastSeconds': 10, 'trimLastSeconds': 8}
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:135 - 开始实际合并目录 F:\github\aicut_auto\原视频合并 中的视频文件
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:136 - 输出文件: F:\github\aicut_auto\原视频合并\cutbefore.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:137 - 时间戳文件: F:\github\aicut_auto\原视频合并\hanger.txt
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:144 - 找到 79 个视频文件
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   1. 1-dbd06aa121c7.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   2. 1.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   3. 2.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   4. 3.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   5. 4.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   6. 5.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   7. 6.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   8. 7.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   9. 8.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   10. 9.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   11. 10.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   12. 11.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   13. 12.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   14. 13.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   15. 14.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   16. 15.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   17. 16.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   18. 17.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   19. 18.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   20. 19.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   21. 20.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   22. 21.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   23. 22.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   24. 23.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   25. 24.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   26. 25.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   27. 26.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   28. 27.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   29. 28.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   30. 29.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   31. 30.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   32. 31.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   33. 32.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   34. 33.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   35. 34.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   36. 35.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   37. 36.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   38. 37.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   39. 38.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   40. 39.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   41. 40.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   42. 41.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   43. 42.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   44. 43.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   45. 44.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   46. 45.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   47. 46.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   48. 47.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   49. 48.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   50. 49.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   51. 50.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   52. 51.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   53. 52.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   54. 53.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   55. 54.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   56. 55.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   57. 56.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   58. 57.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   59. 58.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   60. 59.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   61. 60.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   62. 61.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   63. 62.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   64. 63.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   65. 64.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   66. 65.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   67. 66.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   68. 67.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   69. 68.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   70. 69.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   71. 70.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   72. 71.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   73. 72.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   74. 73.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   75. 74.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   76. 75.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   77. 76.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   78. 77.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:146 -   79. 78.mp4
2025-08-16 14:51:26 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:150 - 使用进程跟踪的视频合并模式
2025-08-16 14:51:27 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 73388
2025-08-16 14:51:27 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 40540
2025-08-16 14:51:27 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 79384
2025-08-16 14:51:27 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 56996
2025-08-16 14:51:27 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 57636
2025-08-16 14:51:27 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 47500
2025-08-16 14:51:27 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 78188
2025-08-16 14:51:27 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 81620
2025-08-16 14:51:27 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 68668
2025-08-16 14:51:27 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 83912
2025-08-16 14:51:27 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 79700
2025-08-16 14:51:27 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 57960
2025-08-16 14:51:27 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 51236
2025-08-16 14:51:27 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 30708
2025-08-16 14:51:27 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 55752
2025-08-16 14:51:27 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 70268
2025-08-16 14:51:27 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 70516
2025-08-16 14:51:27 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 59076
2025-08-16 14:51:27 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 86092
2025-08-16 14:51:27 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 79808
2025-08-16 14:51:27 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 52832
2025-08-16 14:51:27 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 85720
2025-08-16 14:51:27 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 21052
2025-08-16 14:51:28 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 53680
2025-08-16 14:51:28 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 88396
2025-08-16 14:51:28 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 74016
2025-08-16 14:51:28 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 82412
2025-08-16 14:51:28 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 65928
2025-08-16 14:51:28 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 57664
2025-08-16 14:51:28 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 84432
2025-08-16 14:51:28 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 65660
2025-08-16 14:51:28 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 80024
2025-08-16 14:51:28 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 33216
2025-08-16 14:51:28 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 19484
2025-08-16 14:51:28 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 3868
2025-08-16 14:51:28 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 81588
2025-08-16 14:51:28 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 64844
2025-08-16 14:51:28 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 79488
2025-08-16 14:51:28 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 2136
2025-08-16 14:51:28 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 81272
2025-08-16 14:51:29 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 84904
2025-08-16 14:51:29 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 61024
2025-08-16 14:51:29 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 47704
2025-08-16 14:51:29 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 65108
2025-08-16 14:51:29 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 76476
2025-08-16 14:51:29 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 80612
2025-08-16 14:51:29 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 83644
2025-08-16 14:51:29 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 57092
2025-08-16 14:51:29 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 71936
2025-08-16 14:51:29 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 88808
2025-08-16 14:51:29 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 75396
2025-08-16 14:51:29 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 77364
2025-08-16 14:51:29 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 87932
2025-08-16 14:51:29 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 84856
2025-08-16 14:51:29 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 54696
2025-08-16 14:51:29 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 68684
2025-08-16 14:51:30 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 57884
2025-08-16 14:51:30 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 68364
2025-08-16 14:51:30 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 82496
2025-08-16 14:51:30 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 58548
2025-08-16 14:51:30 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 82328
2025-08-16 14:51:30 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 85152
2025-08-16 14:51:30 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 66076
2025-08-16 14:51:30 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 61588
2025-08-16 14:51:30 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 77704
2025-08-16 14:51:30 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 62456
2025-08-16 14:51:30 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 32940
2025-08-16 14:51:30 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 82632
2025-08-16 14:51:30 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 82432
2025-08-16 14:51:30 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 82016
2025-08-16 14:51:30 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 88456
2025-08-16 14:51:30 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 38100
2025-08-16 14:51:30 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 28804
2025-08-16 14:51:31 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 88744
2025-08-16 14:51:31 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 71244
2025-08-16 14:51:31 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 81904
2025-08-16 14:51:31 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 68792
2025-08-16 14:51:31 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 85156
2025-08-16 14:51:31 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 88348
2025-08-16 14:51:31 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 84852
2025-08-16 14:51:31 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 83432
2025-08-16 14:51:31 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 14888
2025-08-16 14:51:31 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 88484
2025-08-16 14:51:31 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 84732
2025-08-16 14:51:31 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 18316
2025-08-16 14:51:31 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 70876
2025-08-16 14:51:31 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 70024
2025-08-16 14:51:31 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 70184
2025-08-16 14:51:31 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 54344
2025-08-16 14:51:31 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 64752
2025-08-16 14:51:32 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 73200
2025-08-16 14:51:32 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 22376
2025-08-16 14:51:32 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 77252
2025-08-16 14:51:32 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 28368
2025-08-16 14:51:32 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 76568
2025-08-16 14:51:32 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 82488
2025-08-16 14:51:32 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 88452
2025-08-16 14:51:32 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 65872
2025-08-16 14:51:32 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 11876
2025-08-16 14:51:32 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 73032
2025-08-16 14:51:32 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 54120
2025-08-16 14:51:32 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 83132
2025-08-16 14:51:32 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 81880
2025-08-16 14:51:32 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 87916
2025-08-16 14:51:32 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 64340
2025-08-16 14:51:32 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 57320
2025-08-16 14:51:32 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 86268
2025-08-16 14:51:32 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 72280
2025-08-16 14:51:33 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 85528
2025-08-16 14:51:33 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 66552
2025-08-16 14:51:33 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 24924
2025-08-16 14:51:33 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 73688
2025-08-16 14:51:33 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 62408
2025-08-16 14:51:33 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 58636
2025-08-16 14:51:33 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 60016
2025-08-16 14:51:33 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 54036
2025-08-16 14:51:33 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 45768
2025-08-16 14:51:33 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 71692
2025-08-16 14:51:33 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 53096
2025-08-16 14:51:33 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 71132
2025-08-16 14:51:33 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 38256
2025-08-16 14:51:33 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 60644
2025-08-16 14:51:33 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 54176
2025-08-16 14:51:33 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 56200
2025-08-16 14:51:34 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 73440
2025-08-16 14:51:34 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 60632
2025-08-16 14:51:34 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 71848
2025-08-16 14:51:34 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 11728
2025-08-16 14:51:34 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 70020
2025-08-16 14:51:34 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 73080
2025-08-16 14:51:34 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 70060
2025-08-16 14:51:34 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 65284
2025-08-16 14:51:34 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 48180
2025-08-16 14:51:34 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 57276
2025-08-16 14:51:34 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 73188
2025-08-16 14:51:34 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 58484
2025-08-16 14:51:34 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 74588
2025-08-16 14:51:34 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 54264
2025-08-16 14:51:34 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 54920
2025-08-16 14:51:34 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 64336
2025-08-16 14:51:34 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 59840
2025-08-16 14:51:35 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 68032
2025-08-16 14:51:35 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 74252
2025-08-16 14:51:35 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 60172
2025-08-16 14:51:35 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 72812
2025-08-16 14:51:35 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 59136
2025-08-16 14:51:35 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 63036
2025-08-16 14:51:35 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 68084
2025-08-16 14:51:35 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 53676
2025-08-16 14:51:35 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 70972
2025-08-16 14:51:35 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 71420
2025-08-16 14:51:35 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 50792
2025-08-16 14:51:35 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 56356
2025-08-16 14:51:35 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 57144
2025-08-16 14:51:35 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 48072
2025-08-16 14:51:35 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 70272
2025-08-16 14:51:35 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 54940
2025-08-16 14:51:35 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 81224
2025-08-16 14:51:36 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 86520
2025-08-16 14:51:36 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 80312
2025-08-16 14:51:36 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 78116
2025-08-16 14:51:36 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 57976
2025-08-16 14:51:36 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 86876
2025-08-16 14:51:36 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 62524
2025-08-16 14:51:36 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 82272
2025-08-16 14:51:36 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 9900
2025-08-16 14:51:36 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 84384
2025-08-16 14:51:36 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 74968
2025-08-16 14:51:36 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 70988
2025-08-16 14:51:36 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 16768
2025-08-16 14:51:36 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 40892
2025-08-16 14:51:36 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 31072
2025-08-16 14:51:36 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 88480
2025-08-16 14:51:36 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 25388
2025-08-16 14:51:36 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 88844
2025-08-16 14:51:37 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 76684
2025-08-16 14:51:37 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 75360
2025-08-16 14:51:37 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 74688
2025-08-16 14:51:37 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 56028
2025-08-16 14:51:37 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 83452
2025-08-16 14:51:37 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 86716
2025-08-16 14:51:37 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 79508
2025-08-16 14:51:37 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 11360
2025-08-16 14:51:37 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 23104
2025-08-16 14:51:37 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 65488
2025-08-16 14:51:37 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 21604
2025-08-16 14:51:37 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 81976
2025-08-16 14:51:37 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 39740
2025-08-16 14:51:37 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 81212
2025-08-16 14:51:37 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 74372
2025-08-16 14:51:37 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 87388
2025-08-16 14:51:37 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 81104
2025-08-16 14:51:38 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 48752
2025-08-16 14:51:38 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 54240
2025-08-16 14:51:38 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 78808
2025-08-16 14:51:38 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 5104
2025-08-16 14:51:38 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 75848
2025-08-16 14:51:38 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 36016
2025-08-16 14:51:38 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 45924
2025-08-16 14:51:38 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 86248
2025-08-16 14:51:38 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 61300
2025-08-16 14:51:38 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 56548
2025-08-16 14:51:38 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 58516
2025-08-16 14:51:38 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 52688
2025-08-16 14:51:38 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 85676
2025-08-16 14:51:38 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 85748
2025-08-16 14:51:38 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 12860
2025-08-16 14:51:38 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 86640
2025-08-16 14:51:38 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 83028
2025-08-16 14:51:39 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 84380
2025-08-16 14:51:39 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 21388
2025-08-16 14:51:39 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 85792
2025-08-16 14:51:39 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 78252
2025-08-16 14:51:39 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 85404
2025-08-16 14:51:39 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 8
2025-08-16 14:51:39 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 54704
2025-08-16 14:51:39 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 86888
2025-08-16 14:51:39 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 18412
2025-08-16 14:51:39 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 82080
2025-08-16 14:51:39 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 59876
2025-08-16 14:51:39 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 84736
2025-08-16 14:51:39 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 80460
2025-08-16 14:51:39 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 76896
2025-08-16 14:51:39 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 79024
2025-08-16 14:51:39 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 53456
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 11656
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 86576
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 81280
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 85688
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 56884
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 44188
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 82280
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 84524
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 51772
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 74828
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 73328
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 88900
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 67020
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 47520
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 23524
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 78952
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 32492
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 83176
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 87672
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 30728
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 162.457687 -i F:\github\aicut_auto\原视频合并\2.mp4...
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 75.893696 -i F:\github\aicut_auto\原视频合并\1-dbd06aa121c7.mp4...
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 136.753197 -i F:\github\aicut_auto\原视频合并\3.mp4...
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 75.893696 -i F:\github\aicut_auto\原视频合并\1.mp4...
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 136.776417 -i F:\github\aicut_auto\原视频合并\6.mp4...
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 100.228209 -i F:\github\aicut_auto\原视频合并\7.mp4...
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 57.967890999999995 -i F:\github\aicut_auto\原视频合并\5.mp4...
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 108.749932 -i F:\github\aicut_auto\原视频合并\4.mp4...
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 86752
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 86632
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 88184
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 70268
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 11488
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 58848
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 84056
2025-08-16 14:51:40 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 76048
2025-08-16 14:51:52 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 63696
2025-08-16 14:51:52 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 70.506667 -i F:\github\aicut_auto\原视频合并\8.mp4...
2025-08-16 14:51:53 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 68860
2025-08-16 14:51:55 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 73032
2025-08-16 14:51:55 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 97.372154 -i F:\github\aicut_auto\原视频合并\9.mp4...
2025-08-16 14:51:55 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 69064
2025-08-16 14:51:55 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 114.183401 -i F:\github\aicut_auto\原视频合并\10.mp4...
2025-08-16 14:51:55 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 71652
2025-08-16 14:51:55 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 81880
2025-08-16 14:51:56 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 71336
2025-08-16 14:51:56 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 120.84752800000001 -i F:\github\aicut_auto\原视频合并\11.mp4...
2025-08-16 14:51:56 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 56004
2025-08-16 14:51:56 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 134.709841 -i F:\github\aicut_auto\原视频合并\12.mp4...
2025-08-16 14:51:56 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 66908
2025-08-16 14:51:56 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 37552
2025-08-16 14:51:58 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 58940
2025-08-16 14:51:58 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 54.647438 -i F:\github\aicut_auto\原视频合并\13.mp4...
2025-08-16 14:51:58 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 74572
2025-08-16 14:51:58 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 59136
2025-08-16 14:51:58 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 84.949478 -i F:\github\aicut_auto\原视频合并\14.mp4...
2025-08-16 14:51:58 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 49752
2025-08-16 14:52:01 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 6312
2025-08-16 14:52:01 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 71.063946 -i F:\github\aicut_auto\原视频合并\15.mp4...
2025-08-16 14:52:01 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 57788
2025-08-16 14:52:02 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 87268
2025-08-16 14:52:02 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 101.482086 -i F:\github\aicut_auto\原视频合并\16.mp4...
2025-08-16 14:52:02 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 55388
2025-08-16 14:52:06 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 88844
2025-08-16 14:52:06 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 64.283719 -i F:\github\aicut_auto\原视频合并\17.mp4...
2025-08-16 14:52:06 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 76684
2025-08-16 14:52:07 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 81688
2025-08-16 14:52:07 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 97.952653 -i F:\github\aicut_auto\原视频合并\18.mp4...
2025-08-16 14:52:08 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 62892
2025-08-16 14:52:10 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 87524
2025-08-16 14:52:10 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 109.237551 -i F:\github\aicut_auto\原视频合并\19.mp4...
2025-08-16 14:52:10 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 46680
2025-08-16 14:52:10 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 48472
2025-08-16 14:52:10 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 119.013152 -i F:\github\aicut_auto\原视频合并\20.mp4...
2025-08-16 14:52:10 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 17688
2025-08-16 14:52:10 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 38716
2025-08-16 14:52:10 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 85.320998 -i F:\github\aicut_auto\原视频合并\21.mp4...
2025-08-16 14:52:10 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 74480
2025-08-16 14:52:12 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 56508
2025-08-16 14:52:12 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 59.082449 -i F:\github\aicut_auto\原视频合并\22.mp4...
2025-08-16 14:52:12 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 67412
2025-08-16 14:52:14 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 51772
2025-08-16 14:52:14 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 93.424762 -i F:\github\aicut_auto\原视频合并\23.mp4...
2025-08-16 14:52:14 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 62268
2025-08-16 14:52:14 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 78004
2025-08-16 14:52:14 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 88.246712 -i F:\github\aicut_auto\原视频合并\24.mp4...
2025-08-16 14:52:14 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 55752
2025-08-16 14:52:15 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 63588
2025-08-16 14:52:15 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 66.094875 -i F:\github\aicut_auto\原视频合并\25.mp4...
2025-08-16 14:52:15 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 46032
2025-08-16 14:52:20 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 62356
2025-08-16 14:52:20 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 77.077914 -i F:\github\aicut_auto\原视频合并\26.mp4...
2025-08-16 14:52:20 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 58876
2025-08-16 14:52:20 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 10788
2025-08-16 14:52:20 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 135.42966 -i F:\github\aicut_auto\原视频合并\27.mp4...
2025-08-16 14:52:20 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 74052
2025-08-16 14:52:22 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 22404
2025-08-16 14:52:22 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 113.068844 -i F:\github\aicut_auto\原视频合并\28.mp4...
2025-08-16 14:52:22 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 61956
2025-08-16 14:52:24 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 85724
2025-08-16 14:52:24 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 89.40771 -i F:\github\aicut_auto\原视频合并\29.mp4...
2025-08-16 14:52:24 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 80680
2025-08-16 14:52:24 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 85720
2025-08-16 14:52:24 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 87.108934 -i F:\github\aicut_auto\原视频合并\30.mp4...
2025-08-16 14:52:24 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 70264
2025-08-16 14:52:25 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 73632
2025-08-16 14:52:25 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 68.95093 -i F:\github\aicut_auto\原视频合并\31.mp4...
2025-08-16 14:52:25 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 88860
2025-08-16 14:52:26 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 75620
2025-08-16 14:52:26 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 84.647619 -i F:\github\aicut_auto\原视频合并\32.mp4...
2025-08-16 14:52:26 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 59136
2025-08-16 14:52:27 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 1340
2025-08-16 14:52:27 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 68.509751 -i F:\github\aicut_auto\原视频合并\33.mp4...
2025-08-16 14:52:27 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 83564
2025-08-16 14:52:31 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 51372
2025-08-16 14:52:31 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 76.427755 -i F:\github\aicut_auto\原视频合并\34.mp4...
2025-08-16 14:52:31 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 61604
2025-08-16 14:52:34 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 2396
2025-08-16 14:52:34 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 67.650612 -i F:\github\aicut_auto\原视频合并\35.mp4...
2025-08-16 14:52:34 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 88648
2025-08-16 14:52:35 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 72116
2025-08-16 14:52:35 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 74.12898 -i F:\github\aicut_auto\原视频合并\36.mp4...
2025-08-16 14:52:35 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 88020
2025-08-16 14:52:36 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 65972
2025-08-16 14:52:36 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 64.190839 -i F:\github\aicut_auto\原视频合并\37.mp4...
2025-08-16 14:52:36 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 44164
2025-08-16 14:52:36 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 71420
2025-08-16 14:52:36 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 101.551746 -i F:\github\aicut_auto\原视频合并\38.mp4...
2025-08-16 14:52:36 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 82752
2025-08-16 14:52:37 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 62524
2025-08-16 14:52:37 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 109.051791 -i F:\github\aicut_auto\原视频合并\39.mp4...
2025-08-16 14:52:37 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 76304
2025-08-16 14:52:38 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 58128
2025-08-16 14:52:38 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 124.86457999999999 -i F:\github\aicut_auto\原视频合并\40.mp4...
2025-08-16 14:52:38 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 83960
2025-08-16 14:52:39 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 59104
2025-08-16 14:52:39 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 140.28263 -i F:\github\aicut_auto\原视频合并\41.mp4...
2025-08-16 14:52:39 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 81976
2025-08-16 14:52:41 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 79508
2025-08-16 14:52:41 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 63.958639000000005 -i F:\github\aicut_auto\原视频合并\42.mp4...
2025-08-16 14:52:41 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 11360
2025-08-16 14:52:43 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 73188
2025-08-16 14:52:43 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 46.868753 -i F:\github\aicut_auto\原视频合并\43.mp4...
2025-08-16 14:52:43 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 71820
2025-08-16 14:52:45 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 74896
2025-08-16 14:52:45 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 63.076280999999994 -i F:\github\aicut_auto\原视频合并\44.mp4...
2025-08-16 14:52:45 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 60720
2025-08-16 14:52:45 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 75876
2025-08-16 14:52:45 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 60.452426 -i F:\github\aicut_auto\原视频合并\45.mp4...
2025-08-16 14:52:45 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 60664
2025-08-16 14:52:49 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 88052
2025-08-16 14:52:49 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 61.149024999999995 -i F:\github\aicut_auto\原视频合并\46.mp4...
2025-08-16 14:52:49 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 78380
2025-08-16 14:52:49 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 86040
2025-08-16 14:52:49 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 100.042449 -i F:\github\aicut_auto\原视频合并\47.mp4...
2025-08-16 14:52:49 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 88232
2025-08-16 14:52:50 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 88772
2025-08-16 14:52:50 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 77.890612 -i F:\github\aicut_auto\原视频合并\48.mp4...
2025-08-16 14:52:50 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 3600
2025-08-16 14:52:52 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 87936
2025-08-16 14:52:52 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 47.495692 -i F:\github\aicut_auto\原视频合并\49.mp4...
2025-08-16 14:52:52 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 73372
2025-08-16 14:52:53 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 55496
2025-08-16 14:52:53 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 53.6722 -i F:\github\aicut_auto\原视频合并\50.mp4...
2025-08-16 14:52:53 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 71512
2025-08-16 14:52:53 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 86464
2025-08-16 14:52:53 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 73.75746 -i F:\github\aicut_auto\原视频合并\51.mp4...
2025-08-16 14:52:53 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 70956
2025-08-16 14:52:55 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 66508
2025-08-16 14:52:55 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 71.017506 -i F:\github\aicut_auto\原视频合并\52.mp4...
2025-08-16 14:52:55 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 74060
2025-08-16 14:52:57 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 88612
2025-08-16 14:52:57 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 83.184762 -i F:\github\aicut_auto\原视频合并\53.mp4...
2025-08-16 14:52:57 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 75600
2025-08-16 14:52:58 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 75092
2025-08-16 14:52:58 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 78.169252 -i F:\github\aicut_auto\原视频合并\54.mp4...
2025-08-16 14:52:58 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 18316
2025-08-16 14:52:59 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 14544
2025-08-16 14:52:59 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 100.088889 -i F:\github\aicut_auto\原视频合并\55.mp4...
2025-08-16 14:52:59 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 53904
2025-08-16 14:53:00 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 78428
2025-08-16 14:53:00 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 91.079546 -i F:\github\aicut_auto\原视频合并\56.mp4...
2025-08-16 14:53:00 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 81596
2025-08-16 14:53:00 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 72512
2025-08-16 14:53:00 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 118.711293 -i F:\github\aicut_auto\原视频合并\57.mp4...
2025-08-16 14:53:00 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 17288
2025-08-16 14:53:03 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 78792
2025-08-16 14:53:03 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 85.413878 -i F:\github\aicut_auto\原视频合并\58.mp4...
2025-08-16 14:53:03 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 85628
2025-08-16 14:53:04 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 78192
2025-08-16 14:53:04 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 85.112018 -i F:\github\aicut_auto\原视频合并\59.mp4...
2025-08-16 14:53:04 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 76840
2025-08-16 14:53:05 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 77364
2025-08-16 14:53:05 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 72.410703 -i F:\github\aicut_auto\原视频合并\60.mp4...
2025-08-16 14:53:05 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 47704
2025-08-16 14:53:08 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 60556
2025-08-16 14:53:08 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 62.472561999999996 -i F:\github\aicut_auto\原视频合并\61.mp4...
2025-08-16 14:53:08 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 66308
2025-08-16 14:53:08 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 33732
2025-08-16 14:53:08 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 100.924807 -i F:\github\aicut_auto\原视频合并\62.mp4...
2025-08-16 14:53:08 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 74556
2025-08-16 14:53:12 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 66020
2025-08-16 14:53:12 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 91.102766 -i F:\github\aicut_auto\原视频合并\63.mp4...
2025-08-16 14:53:12 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 67868
2025-08-16 14:53:12 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 77584
2025-08-16 14:53:12 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 117.875374 -i F:\github\aicut_auto\原视频合并\64.mp4...
2025-08-16 14:53:12 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 73096
2025-08-16 14:53:14 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 55844
2025-08-16 14:53:14 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 145.64644 -i F:\github\aicut_auto\原视频合并\65.mp4...
2025-08-16 14:53:14 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 72716
2025-08-16 14:53:15 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 11628
2025-08-16 14:53:15 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 106.009977 -i F:\github\aicut_auto\原视频合并\66.mp4...
2025-08-16 14:53:15 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 71488
2025-08-16 14:53:16 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 18120
2025-08-16 14:53:16 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 99.34585 -i F:\github\aicut_auto\原视频合并\67.mp4...
2025-08-16 14:53:16 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 78004
2025-08-16 14:53:16 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 64028
2025-08-16 14:53:16 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 137.426576 -i F:\github\aicut_auto\原视频合并\68.mp4...
2025-08-16 14:53:16 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 78620
2025-08-16 14:53:16 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 2424
2025-08-16 14:53:16 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 114.206621 -i F:\github\aicut_auto\原视频合并\69.mp4...
2025-08-16 14:53:16 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 33652
2025-08-16 14:53:22 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 70820
2025-08-16 14:53:22 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 144.299683 -i F:\github\aicut_auto\原视频合并\70.mp4...
2025-08-16 14:53:22 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 85068
2025-08-16 14:53:24 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 54360
2025-08-16 14:53:24 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 117.039456 -i F:\github\aicut_auto\原视频合并\71.mp4...
2025-08-16 14:53:24 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 83356
2025-08-16 14:53:28 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 64792
2025-08-16 14:53:28 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 104.73288 -i F:\github\aicut_auto\原视频合并\72.mp4...
2025-08-16 14:53:28 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 70424
2025-08-16 14:53:29 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 81764
2025-08-16 14:53:29 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 106.938776 -i F:\github\aicut_auto\原视频合并\73.mp4...
2025-08-16 14:53:29 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 68364
2025-08-16 14:53:29 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 23048
2025-08-16 14:53:29 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 98.231293 -i F:\github\aicut_auto\原视频合并\74.mp4...
2025-08-16 14:53:29 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 64380
2025-08-16 14:53:33 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 78252
2025-08-16 14:53:33 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 101.900045 -i F:\github\aicut_auto\原视频合并\75.mp4...
2025-08-16 14:53:33 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 85156
2025-08-16 14:53:33 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 87948
2025-08-16 14:53:33 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 131.342948 -i F:\github\aicut_auto\原视频合并\76.mp4...
2025-08-16 14:53:33 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 55500
2025-08-16 14:53:34 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 52688
2025-08-16 14:53:34 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 118.409433 -i F:\github\aicut_auto\原视频合并\77.mp4...
2025-08-16 14:53:34 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 72904
2025-08-16 14:53:40 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 77104
2025-08-16 14:53:40 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -to 68.161451 -i F:\github\aicut_auto\原视频合并\78.mp4...
2025-08-16 14:53:40 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 43324
2025-08-16 14:53:46 - aicut_auto.core.video_merger - INFO - track_ffmpeg_run:248 - 将执行ffmpeg命令: ffmpeg -f concat -safe 0...
2025-08-16 14:53:46 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 61332
2025-08-16 14:54:09 - aicut_auto.core.video_merger - INFO - track_subprocess_popen:233 - 已注册Popen进程 PID: 28176
2025-08-16 14:54:10 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:157 - 视频合并完成: F:\github\aicut_auto\原视频合并\cutbefore.mp4
2025-08-16 14:54:10 - aicut_auto.core.video_merger - INFO - _merge_videos_sync:158 - 时间戳信息已保存到: F:\github\aicut_auto\原视频合并\hanger.txt
2025-08-16 14:54:10 - aicut_auto.core.video_merger - INFO - merge_videos_async:97 - 视频合并任务完成
2025-08-16 15:07:34 - aicut_auto.core.ai_video_splitter - INFO - __init__:36 - AI视频分割器已启用进程跟踪，task_id: 529edfb8-7693-42e5-af9b-ad58b90a3967
2025-08-16 15:07:34 - aicut_auto.core.ai_video_splitter - INFO - split_video:61 - 开始AI视频分割任务，参数: {'inputVideoPath': 'F:\\github\\aicut_auto\\原视频合并\\cutbefore.mp4', 'outputDirPath': 'F:\\github\\aicut_auto\\ai-video-splitter', 'frameBatchSize': 2000, 'sceneBatchSize': 500, 'videoEncodePreset': 'p4', 'videoBitrate': '5M', 'processMode': 'fast'}
2025-08-16 15:07:34 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:129 - AI视频分割开始:
2025-08-16 15:07:34 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:130 -   输入视频: F:\github\aicut_auto\原视频合并\cutbefore.mp4
2025-08-16 15:07:34 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:131 -   输出目录: F:\github\aicut_auto\ai-video-splitter
2025-08-16 15:07:34 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:132 -   帧批量大小: 2000
2025-08-16 15:07:34 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:133 -   场景批量大小: 500
2025-08-16 15:07:34 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:134 -   视频编码预设: p4
2025-08-16 15:07:34 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:135 -   视频码率: 5M
2025-08-16 15:07:34 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:136 -   处理模式: fast
2025-08-16 15:07:34 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:160 - 使用字幕文件: F:\github\aicut_auto\原视频合并\cutbefore.srt
2025-08-16 15:07:34 - aicut_auto.core.ai_video_splitter - INFO - _call_ai_splitter_script:186 - 进度: 10% - 准备调用AI分割脚本...
2025-08-16 15:07:34 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:206 - 使用subprocess方式调用AI分割脚本...
2025-08-16 15:07:34 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:207 - 进度: 20% - 启动AI视频分割进程...
2025-08-16 15:07:34 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:226 - 使用Python解释器: F:\github\aicut_auto\.venv\Scripts\python.exe
2025-08-16 15:07:34 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:227 - 执行命令: F:\github\aicut_auto\.venv\Scripts\python.exe F:\github\aicut_auto\2-2-2_ai_video_splitter(全视频场景分割).py F:\github\aicut_auto\原视频合并\cutbefore.mp4 F:\github\aicut_auto\原视频合并\cutbefore.srt F:\github\aicut_auto\ai-video-splitter --frame-batch 2000 --scene-batch 500 --encode-preset p4 --bitrate 5M
2025-08-16 15:07:34 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:248 - 已注册AI分割子进程PID: 78932
2025-08-16 15:25:29 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:332 - 进度: 100% - AI视频分割完成
2025-08-16 15:25:29 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:333 - AI分割脚本执行成功
2025-08-16 15:25:29 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:336 - 脚本stderr输出:
2025-08-16 15:25:29 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   2025-08-16 15:07:38.404781: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-08-16 15:25:29 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   2025-08-16 15:07:39.315988: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-08-16 15:25:29 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   2025-08-16 15:07:45.916493: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.
2025-08-16 15:25:29 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   To enable the following instructions: SSE3 SSE4.1 SSE4.2 AVX AVX2 AVX_VNNI FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.
2025-08-16 15:25:29 - aicut_auto.core.ai_video_splitter - INFO - _count_generated_files:404 - 生成了 3221 个场景文件
2025-08-16 15:25:29 - aicut_auto.core.ai_video_splitter - INFO - split_video:92 - AI视频分割任务完成
