#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
视频关键帧分析工具

该工具用于批量分析Keyframes目录中的图片，使用Gemini Balance API进行图片内容分析。

功能特点:
1. 扫描F:\\github\\aicut_auto\\ai-video-splitter\\Keyframes目录中的图片
2. 按scene编号排序图片文件（支持scene001到scene999）
3. 将图片转换为base64格式
4. 使用Gemini Balance API分析图片内容
5. 支持测试模式（先分析2张图片）

使用方法:
python keyframe_analyzer.py [--test] [--count N]
"""

import os
import re
import json
import base64
import logging
import requests
import argparse
import threading
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Tuple, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed

# 设置日志格式
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("keyframe_analyzer.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("关键帧分析器")

# 线程安全锁
json_update_lock = threading.Lock()

# 配置常量
KEYFRAMES_DIR = r"F:\github\aicut_auto\ai-video-splitter\Keyframes"
OUTPUT_DIR = r"F:\github\aicut_auto"
SCENES_REPORT_PATH = r"F:\github\aicut_auto\ai-video-splitter\scenes_report.json"

# 全局处理配置（可根据需要调整）
ANALYSIS_COUNT = 2  # 每次API提交的图片数量
MAX_RETRY_ATTEMPTS = 3  # 最大重试次数
PARALLEL_THREADS = 5  # 并行处理的线程数（建议根据API服务器性能调整）

# Gemini Balance API配置（参考2-3_ai_video_generator.py）
GEMINI_BALANCE_API_URL = "http://192.168.100.159:7770/v1/chat/completions"
GEMINI_BALANCE_API_KEY = "123456"
GEMINI_BALANCE_MODEL = "gemini-2.5-flash-preview-05-20"

# 提示词模板
PROMPT_TEMPLATE = """我提供了 %s 张视频帧，它们按时间顺序排列，代表一个连续的视频片段。请仔细分析每一帧的内容，并关注帧与帧之间的变化，以理解整个片段的活动。

首先，请详细描述每一帧的关键视觉信息（包含：主要内容、人物、动作和场景）。
然后，基于所有帧的分析，请用**简洁的语言**总结整个视频片段中发生的主要活动或事件流程。

请务必使用 JSON 格式输出你的结果。JSON 结构应如下：
{
  "frame_observations": [
    {
      "frame_number": 1, // 或其他标识帧的方式
      "observation": "描述每张视频帧中的主要内容、人物、动作和场景。"
    },
    // ... 更多帧的观察 ...
  ],
  "overall_activity_summary": "在这里填写你总结的整个片段的主要活动，保持简洁。"
}

请务必不要遗漏视频帧，我提供了 %s 张视频帧，frame_observations 必须包含 %s 个元素

请只返回 JSON 字符串，不要包含任何其他解释性文字。"""


def load_scenes_report(file_path: str) -> Optional[Dict]:
    """
    加载scenes_report.json文件

    Args:
        file_path: JSON文件路径

    Returns:
        解析后的JSON数据，失败返回None
    """
    try:
        if not os.path.exists(file_path):
            logger.error(f"scenes_report.json文件不存在: {file_path}")
            return None

        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 验证JSON格式
        if not isinstance(data, dict) or 'scenes' not in data:
            logger.error("scenes_report.json格式不正确，缺少'scenes'字段")
            return None

        logger.info(f"成功加载scenes_report.json，包含 {len(data.get('scenes', []))} 个场景")
        return data

    except json.JSONDecodeError as e:
        logger.error(f"scenes_report.json格式错误: {e}")
        return None
    except Exception as e:
        logger.error(f"加载scenes_report.json时出错: {e}")
        return None


def save_scenes_report(data: Dict, file_path: str) -> bool:
    """
    保存scenes_report.json文件

    Args:
        data: 要保存的数据
        file_path: JSON文件路径

    Returns:
        保存是否成功
    """
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        logger.info(f"成功保存scenes_report.json: {file_path}")
        return True
    except Exception as e:
        logger.error(f"保存scenes_report.json时出错: {e}")
        return False


def validate_api_response(response_text: str, expected_count: int) -> Tuple[bool, Optional[List[Dict]]]:
    """
    验证API响应格式和内容

    Args:
        response_text: API返回的文本
        expected_count: 期望的frame数量

    Returns:
        (是否有效, 解析后的observations列表)
    """
    try:
        # 清理响应文本，移除可能的markdown标记
        cleaned_text = response_text.strip()
        if cleaned_text.startswith('```json'):
            cleaned_text = cleaned_text[7:]
        if cleaned_text.endswith('```'):
            cleaned_text = cleaned_text[:-3]
        cleaned_text = cleaned_text.strip()

        # 解析JSON
        try:
            data = json.loads(cleaned_text)
        except json.JSONDecodeError:
            logger.error("API返回的不是有效的JSON格式")
            return False, None

        # 验证结构
        if not isinstance(data, dict):
            logger.error("API返回的JSON不是字典格式")
            return False, None

        if 'frame_observations' not in data:
            logger.error("API返回的JSON缺少'frame_observations'字段")
            return False, None

        observations = data['frame_observations']
        if not isinstance(observations, list):
            logger.error("'frame_observations'不是列表格式")
            return False, None

        if len(observations) != expected_count:
            logger.error(f"frame_observations数量不匹配: 期望{expected_count}，实际{len(observations)}")
            return False, None

        # 验证每个observation的格式
        for i, obs in enumerate(observations):
            if not isinstance(obs, dict):
                logger.error(f"第{i+1}个observation不是字典格式")
                return False, None
            if 'observation' not in obs:
                logger.error(f"第{i+1}个observation缺少'observation'字段")
                return False, None

        logger.info(f"API响应验证通过，包含{len(observations)}个有效的observation")
        return True, observations

    except Exception as e:
        logger.error(f"验证API响应时出错: {e}")
        return False, None


class KeyframeAnalyzer:
    """关键帧分析器类"""

    def __init__(self, keyframes_dir: str = KEYFRAMES_DIR):
        """
        初始化分析器

        Args:
            keyframes_dir: 关键帧图片目录路径
        """
        self.keyframes_dir = keyframes_dir
        self.image_files = []

    def scan_keyframes_directory(self) -> List[str]:
        """
        扫描Keyframes目录，获取所有图片文件

        Returns:
            图片文件路径列表
        """
        try:
            logger.info(f"开始扫描目录: {self.keyframes_dir}")

            if not os.path.exists(self.keyframes_dir):
                logger.error(f"目录不存在: {self.keyframes_dir}")
                return []

            # 获取所有png文件
            image_files = []
            for file in os.listdir(self.keyframes_dir):
                if file.lower().endswith('.png') and 'keyframe_scene' in file and '_start' in file:
                    image_files.append(os.path.join(self.keyframes_dir, file))

            logger.info(f"找到 {len(image_files)} 个关键帧图片文件")
            return image_files

        except Exception as e:
            logger.error(f"扫描目录时出错: {e}")
            return []

    def extract_scene_number(self, filename: str) -> int:
        """
        从文件名中提取scene编号

        Args:
            filename: 文件名

        Returns:
            scene编号，如果提取失败返回-1
        """
        try:
            # 匹配 keyframe_scene001_start.png 格式
            match = re.search(r'keyframe_scene(\d+)_start\.png', filename)
            if match:
                return int(match.group(1))
            else:
                logger.warning(f"无法从文件名提取scene编号: {filename}")
                return -1
        except Exception as e:
            logger.error(f"提取scene编号时出错: {e}")
            return -1

    def sort_images_by_scene(self, image_files: List[str]) -> List[Tuple[int, str]]:
        """
        按scene编号排序图片文件

        Args:
            image_files: 图片文件路径列表

        Returns:
            排序后的(scene编号, 文件路径)元组列表
        """
        try:
            logger.info("开始按scene编号排序图片文件")

            # 提取scene编号并创建元组列表
            scene_files = []
            for file_path in image_files:
                filename = os.path.basename(file_path)
                scene_num = self.extract_scene_number(filename)
                if scene_num != -1:
                    scene_files.append((scene_num, file_path))
                else:
                    logger.warning(f"跳过无法解析的文件: {filename}")

            # 按scene编号排序
            scene_files.sort(key=lambda x: x[0])

            logger.info(f"成功排序 {len(scene_files)} 个图片文件")
            if scene_files:
                logger.info(f"Scene编号范围: {scene_files[0][0]} - {scene_files[-1][0]}")

            return scene_files

        except Exception as e:
            logger.error(f"排序图片文件时出错: {e}")
            return []

    def image_to_base64(self, image_path: str) -> Optional[str]:
        """
        将图片文件转换为base64编码

        Args:
            image_path: 图片文件路径

        Returns:
            base64编码字符串，失败返回None
        """
        try:
            with open(image_path, 'rb') as image_file:
                image_data = image_file.read()
                base64_string = base64.b64encode(image_data).decode('utf-8')
                return base64_string
        except Exception as e:
            logger.error(f"转换图片为base64时出错 {image_path}: {e}")
            return None



    def call_gemini_balance_api_with_retry(self, images_data: List[Dict]) -> Tuple[bool, Optional[List[Dict]]]:
        """
        调用Gemini Balance API分析图片（带重试机制）

        Args:
            images_data: 包含图片信息的字典列表

        Returns:
            (是否成功, observations列表)
        """
        image_count = len(images_data)
        scene_ids = [img['scene_number'] for img in images_data]

        for attempt in range(1, MAX_RETRY_ATTEMPTS + 1):
            try:
                logger.info(f"第{attempt}次尝试调用API分析scene {scene_ids}")

                # 构建提示词
                prompt = PROMPT_TEMPLATE % (image_count, image_count, image_count)

                # 构建消息内容
                content = [{"type": "text", "text": prompt}]

                # 添加图片数据
                for img_data in images_data:
                    content.append({
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{img_data['base64_data']}"
                        }
                    })

                # 构建API请求
                payload = {
                    "model": GEMINI_BALANCE_MODEL,
                    "messages": [
                        {
                            "role": "user",
                            "content": content
                        }
                    ],
                    "stream": False
                }

                # 发送请求
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {GEMINI_BALANCE_API_KEY}"
                }

                response = requests.post(GEMINI_BALANCE_API_URL, json=payload, headers=headers, timeout=240)
                response.raise_for_status()

                result = response.json()
                analysis_result = result.get("choices", [{}])[0].get("message", {}).get("content", "").strip()

                if not analysis_result:
                    logger.error(f"第{attempt}次尝试: API返回空结果")
                    continue

                # 验证API响应
                is_valid, observations = validate_api_response(analysis_result, image_count)
                if is_valid:
                    logger.info(f"第{attempt}次尝试成功，获得有效的分析结果")
                    return True, observations
                else:
                    logger.error(f"第{attempt}次尝试: API响应格式验证失败")

            except requests.exceptions.RequestException as e:
                logger.error(f"第{attempt}次尝试: API请求失败: {e}")
            except Exception as e:
                logger.error(f"第{attempt}次尝试: 处理API响应时出错: {e}")

        logger.error(f"所有{MAX_RETRY_ATTEMPTS}次尝试都失败，scene {scene_ids} 分析失败")
        return False, None

    def update_scenes_report_thread_safe(self, scenes_data: Dict, batch_scenes: List[int], observations: List[Dict]) -> bool:
        """
        线程安全地更新scenes_report.json中的observation字段

        Args:
            scenes_data: scenes_report.json的数据
            batch_scenes: 当前批次的scene编号列表
            observations: API返回的observations列表

        Returns:
            更新是否成功
        """
        try:
            with json_update_lock:  # 使用锁确保线程安全
                scenes_list = scenes_data.get('scenes', [])
                updated_count = 0

                for i, scene_id in enumerate(batch_scenes):
                    if i < len(observations):
                        observation_text = observations[i].get('observation', '')

                        # 查找对应的scene
                        scene_found = False
                        for scene in scenes_list:
                            if scene.get('scene_id') == scene_id:
                                scene['observation'] = observation_text
                                scene_found = True
                                updated_count += 1
                                logger.info(f"已更新scene_id {scene_id} 的observation")
                                break

                        if not scene_found:
                            logger.warning(f"未找到scene_id {scene_id} 对应的场景")

                logger.info(f"成功更新 {updated_count} 个场景的observation")
                return updated_count > 0

        except Exception as e:
            logger.error(f"更新scenes_report时出错: {e}")
            return False

    def process_single_batch(self, batch_idx: int, batch_scene_files: List[Tuple[int, str]], scenes_data: Dict) -> Tuple[int, bool, List[int]]:
        """
        处理单个批次的图片

        Args:
            batch_idx: 批次索引
            batch_scene_files: 当前批次的(scene编号, 文件路径)元组列表
            scenes_data: scenes_report.json的数据

        Returns:
            (批次索引, 是否成功, 失败的scene_id列表)
        """
        batch_scene_ids = [scene_num for scene_num, _ in batch_scene_files]
        failed_scene_ids = []

        try:
            logger.info(f"线程处理第 {batch_idx + 1} 批: scene {batch_scene_ids}")

            # 准备当前批次的图片数据
            batch_images_data = []
            for scene_num, file_path in batch_scene_files:
                base64_data = self.image_to_base64(file_path)
                if base64_data:
                    batch_images_data.append({
                        'scene_number': scene_num,
                        'file_path': file_path,
                        'filename': os.path.basename(file_path),
                        'base64_data': base64_data
                    })
                else:
                    logger.error(f"  跳过无法转换的图片: {file_path}")
                    failed_scene_ids.append(scene_num)

            if not batch_images_data:
                logger.error(f"第 {batch_idx + 1} 批没有有效的图片数据")
                return batch_idx, False, batch_scene_ids

            # 调用API分析（带重试）
            success, observations = self.call_gemini_balance_api_with_retry(batch_images_data)

            if success and observations:
                # 线程安全地更新scenes_report.json
                if self.update_scenes_report_thread_safe(scenes_data, batch_scene_ids, observations):
                    logger.info(f"第 {batch_idx + 1} 批处理成功")
                    return batch_idx, True, []
                else:
                    logger.error(f"第 {batch_idx + 1} 批更新JSON失败")
                    return batch_idx, False, batch_scene_ids
            else:
                logger.error(f"第 {batch_idx + 1} 批API分析失败")
                return batch_idx, False, batch_scene_ids

        except Exception as e:
            logger.error(f"处理第 {batch_idx + 1} 批时出错: {e}")
            return batch_idx, False, batch_scene_ids

    def process_images_in_batches_parallel(self, scene_files: List[Tuple[int, str]], scenes_data: Dict) -> Tuple[int, List[int]]:
        """
        并行分批处理图片并更新scenes_report.json

        Args:
            scene_files: 排序后的(scene编号, 文件路径)元组列表
            scenes_data: scenes_report.json的数据

        Returns:
            (成功处理的批次数, 失败的scene_id列表)
        """
        total_images = len(scene_files)
        total_batches = (total_images + ANALYSIS_COUNT - 1) // ANALYSIS_COUNT
        successful_batches = 0
        failed_scene_ids = []

        logger.info(f"开始并行分批处理 {total_images} 张图片，共 {total_batches} 批，每批 {ANALYSIS_COUNT} 张")
        logger.info(f"使用 {PARALLEL_THREADS} 个并行线程")

        # 准备所有批次的数据
        batch_tasks = []
        for batch_idx in range(total_batches):
            start_idx = batch_idx * ANALYSIS_COUNT
            end_idx = min(start_idx + ANALYSIS_COUNT, total_images)
            batch_scene_files = scene_files[start_idx:end_idx]
            batch_tasks.append((batch_idx, batch_scene_files))

        # 使用线程池并行处理
        with ThreadPoolExecutor(max_workers=PARALLEL_THREADS) as executor:
            # 提交所有任务
            future_to_batch = {
                executor.submit(self.process_single_batch, batch_idx, batch_scene_files, scenes_data): batch_idx
                for batch_idx, batch_scene_files in batch_tasks
            }

            # 收集结果
            completed_batches = []
            for future in as_completed(future_to_batch):
                batch_idx = future_to_batch[future]
                try:
                    result_batch_idx, success, batch_failed_ids = future.result()
                    completed_batches.append((result_batch_idx, success, batch_failed_ids))

                    if success:
                        successful_batches += 1
                    else:
                        failed_scene_ids.extend(batch_failed_ids)

                except Exception as e:
                    logger.error(f"批次 {batch_idx + 1} 执行时出现异常: {e}")
                    # 获取该批次的scene_ids
                    batch_scene_files = batch_tasks[batch_idx][1]
                    batch_scene_ids = [scene_num for scene_num, _ in batch_scene_files]
                    failed_scene_ids.extend(batch_scene_ids)

        # 按批次顺序排序结果（用于日志输出）
        completed_batches.sort(key=lambda x: x[0])

        logger.info(f"\n并行处理完成: 成功 {successful_batches}/{total_batches} 批")
        if failed_scene_ids:
            logger.error(f"失败的scene_id: {failed_scene_ids}")

        return successful_batches, failed_scene_ids

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='视频关键帧分析工具')
    parser.add_argument('--limit', type=int, help='限制处理的图片数量（用于测试）')

    args = parser.parse_args()

    logger.info("=" * 60)
    logger.info("视频关键帧分析工具启动")
    logger.info(f"每批处理图片数量: {ANALYSIS_COUNT}")
    logger.info(f"最大重试次数: {MAX_RETRY_ATTEMPTS}")
    logger.info(f"并行线程数: {PARALLEL_THREADS}")
    logger.info("=" * 60)

    # 检查scenes_report.json文件
    logger.info("检查scenes_report.json文件...")
    scenes_data = load_scenes_report(SCENES_REPORT_PATH)
    if scenes_data is None:
        logger.error("无法加载scenes_report.json文件，程序终止")
        logger.error("请确保文件存在且格式正确")
        return

    # 创建分析器实例
    analyzer = KeyframeAnalyzer()

    # 扫描图片文件
    image_files = analyzer.scan_keyframes_directory()
    if not image_files:
        logger.error("未找到任何图片文件，程序退出")
        return

    # 按scene编号排序
    scene_files = analyzer.sort_images_by_scene(image_files)
    if not scene_files:
        logger.error("排序失败，程序退出")
        return

    # 如果指定了限制数量，则只处理前N张图片
    if args.limit:
        scene_files = scene_files[:args.limit]
        logger.info(f"限制处理图片数量为: {args.limit}")

    logger.info(f"准备处理 {len(scene_files)} 张图片")

    # 并行分批处理图片
    successful_batches, failed_scene_ids = analyzer.process_images_in_batches_parallel(scene_files, scenes_data)

    # 保存更新后的scenes_report.json
    if successful_batches > 0:
        if save_scenes_report(scenes_data, SCENES_REPORT_PATH):
            logger.info("scenes_report.json已成功更新")
        else:
            logger.error("保存scenes_report.json失败")

    # 输出最终报告
    logger.info("\n" + "=" * 60)
    logger.info("处理完成报告")
    logger.info("=" * 60)
    logger.info(f"总图片数量: {len(scene_files)}")
    logger.info(f"成功处理批次: {successful_batches}")

    if failed_scene_ids:
        logger.error(f"失败的scene_id数量: {len(failed_scene_ids)}")
        logger.error(f"失败的scene_id列表: {failed_scene_ids}")
        logger.error("建议检查:")
        logger.error("1. 网络连接是否正常")
        logger.error("2. API服务是否可用")
        logger.error("3. 图片文件是否损坏")
    else:
        logger.info("所有图片处理成功！")

    logger.info("=" * 60)


if __name__ == "__main__":
    main()