#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
视频关键帧分析工具

该工具用于批量分析Keyframes目录中的图片，使用Gemini Balance API进行图片内容分析。

功能特点:
1. 扫描F:\\github\\aicut_auto\\ai-video-splitter\\Keyframes目录中的图片
2. 按scene编号排序图片文件（支持scene001到scene999）
3. 将图片转换为base64格式
4. 使用Gemini Balance API分析图片内容
5. 支持测试模式（先分析2张图片）

使用方法:
python keyframe_analyzer.py [--test] [--count N]
"""

import os
import re
import json
import base64
import logging
import requests
import argparse
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Tuple, Optional

# 设置日志格式
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("keyframe_analyzer.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("关键帧分析器")

# 配置常量
KEYFRAMES_DIR = r"F:\github\aicut_auto\ai-video-splitter\Keyframes"
OUTPUT_DIR = r"F:\github\aicut_auto"

# Gemini Balance API配置（参考2-3_ai_video_generator.py）
GEMINI_BALANCE_API_URL = "http://192.168.100.159:7770/v1/chat/completions"
GEMINI_BALANCE_API_KEY = "123456"
GEMINI_BALANCE_MODEL = "gemini-2.5-flash-preview-05-20"

# 提示词模板
PROMPT_TEMPLATE = """我提供了 %s 张视频帧，它们按时间顺序排列，代表一个连续的视频片段。请仔细分析每一帧的内容，并关注帧与帧之间的变化，以理解整个片段的活动。

首先，请详细描述每一帧的关键视觉信息（包含：主要内容、人物、动作和场景）。
然后，基于所有帧的分析，请用**简洁的语言**总结整个视频片段中发生的主要活动或事件流程。

请务必使用 JSON 格式输出你的结果。JSON 结构应如下：
{
  "frame_observations": [
    {
      "frame_number": 1, // 或其他标识帧的方式
      "observation": "描述每张视频帧中的主要内容、人物、动作和场景。"
    },
    // ... 更多帧的观察 ...
  ],
  "overall_activity_summary": "在这里填写你总结的整个片段的主要活动，保持简洁。"
}

请务必不要遗漏视频帧，我提供了 %s 张视频帧，frame_observations 必须包含 %s 个元素

请只返回 JSON 字符串，不要包含任何其他解释性文字。"""


class KeyframeAnalyzer:
    """关键帧分析器类"""

    def __init__(self, keyframes_dir: str = KEYFRAMES_DIR):
        """
        初始化分析器

        Args:
            keyframes_dir: 关键帧图片目录路径
        """
        self.keyframes_dir = keyframes_dir
        self.image_files = []

    def scan_keyframes_directory(self) -> List[str]:
        """
        扫描Keyframes目录，获取所有图片文件

        Returns:
            图片文件路径列表
        """
        try:
            logger.info(f"开始扫描目录: {self.keyframes_dir}")

            if not os.path.exists(self.keyframes_dir):
                logger.error(f"目录不存在: {self.keyframes_dir}")
                return []

            # 获取所有png文件
            image_files = []
            for file in os.listdir(self.keyframes_dir):
                if file.lower().endswith('.png') and 'keyframe_scene' in file and '_start' in file:
                    image_files.append(os.path.join(self.keyframes_dir, file))

            logger.info(f"找到 {len(image_files)} 个关键帧图片文件")
            return image_files

        except Exception as e:
            logger.error(f"扫描目录时出错: {e}")
            return []

    def extract_scene_number(self, filename: str) -> int:
        """
        从文件名中提取scene编号

        Args:
            filename: 文件名

        Returns:
            scene编号，如果提取失败返回-1
        """
        try:
            # 匹配 keyframe_scene001_start.png 格式
            match = re.search(r'keyframe_scene(\d+)_start\.png', filename)
            if match:
                return int(match.group(1))
            else:
                logger.warning(f"无法从文件名提取scene编号: {filename}")
                return -1
        except Exception as e:
            logger.error(f"提取scene编号时出错: {e}")
            return -1

    def sort_images_by_scene(self, image_files: List[str]) -> List[Tuple[int, str]]:
        """
        按scene编号排序图片文件

        Args:
            image_files: 图片文件路径列表

        Returns:
            排序后的(scene编号, 文件路径)元组列表
        """
        try:
            logger.info("开始按scene编号排序图片文件")

            # 提取scene编号并创建元组列表
            scene_files = []
            for file_path in image_files:
                filename = os.path.basename(file_path)
                scene_num = self.extract_scene_number(filename)
                if scene_num != -1:
                    scene_files.append((scene_num, file_path))
                else:
                    logger.warning(f"跳过无法解析的文件: {filename}")

            # 按scene编号排序
            scene_files.sort(key=lambda x: x[0])

            logger.info(f"成功排序 {len(scene_files)} 个图片文件")
            if scene_files:
                logger.info(f"Scene编号范围: {scene_files[0][0]} - {scene_files[-1][0]}")

            return scene_files

        except Exception as e:
            logger.error(f"排序图片文件时出错: {e}")
            return []

    def image_to_base64(self, image_path: str) -> Optional[str]:
        """
        将图片文件转换为base64编码

        Args:
            image_path: 图片文件路径

        Returns:
            base64编码字符串，失败返回None
        """
        try:
            with open(image_path, 'rb') as image_file:
                image_data = image_file.read()
                base64_string = base64.b64encode(image_data).decode('utf-8')
                return base64_string
        except Exception as e:
            logger.error(f"转换图片为base64时出错 {image_path}: {e}")
            return None

    def prepare_images_for_analysis(self, scene_files: List[Tuple[int, str]], count: int = None) -> List[Dict]:
        """
        准备图片数据用于分析

        Args:
            scene_files: 排序后的(scene编号, 文件路径)元组列表
            count: 要处理的图片数量，None表示处理所有

        Returns:
            包含图片信息的字典列表
        """
        try:
            if count is not None:
                scene_files = scene_files[:count]
                logger.info(f"限制处理图片数量为: {count}")

            images_data = []
            for scene_num, file_path in scene_files:
                logger.info(f"处理图片: scene{scene_num:03d} - {os.path.basename(file_path)}")

                # 转换为base64
                base64_data = self.image_to_base64(file_path)
                if base64_data:
                    images_data.append({
                        'scene_number': scene_num,
                        'file_path': file_path,
                        'filename': os.path.basename(file_path),
                        'base64_data': base64_data
                    })
                else:
                    logger.error(f"跳过无法转换的图片: {file_path}")

            logger.info(f"成功准备 {len(images_data)} 张图片用于分析")
            return images_data

        except Exception as e:
            logger.error(f"准备图片数据时出错: {e}")
            return []

    def call_gemini_balance_api(self, images_data: List[Dict]) -> Optional[str]:
        """
        调用Gemini Balance API分析图片

        Args:
            images_data: 包含图片信息的字典列表

        Returns:
            API返回的分析结果，失败返回None
        """
        try:
            image_count = len(images_data)
            logger.info(f"开始调用Gemini Balance API分析 {image_count} 张图片")

            # 构建提示词
            prompt = PROMPT_TEMPLATE % (image_count, image_count, image_count)

            # 构建消息内容
            content = [{"type": "text", "text": prompt}]

            # 添加图片数据
            for img_data in images_data:
                content.append({
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{img_data['base64_data']}"
                    }
                })

            # 构建API请求
            payload = {
                "model": GEMINI_BALANCE_MODEL,
                "messages": [
                    {
                        "role": "user",
                        "content": content
                    }
                ],
                "stream": False
            }

            # 发送请求
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {GEMINI_BALANCE_API_KEY}"
            }

            logger.info(f"正在调用API: {GEMINI_BALANCE_API_URL}")
            response = requests.post(GEMINI_BALANCE_API_URL, json=payload, headers=headers, timeout=240)
            response.raise_for_status()

            result = response.json()
            analysis_result = result.get("choices", [{}])[0].get("message", {}).get("content", "").strip()

            if analysis_result:
                logger.info("API调用成功，获得分析结果")
                return analysis_result
            else:
                logger.error("API返回空结果")
                return None

        except requests.exceptions.RequestException as e:
            logger.error(f"调用Gemini Balance API失败: {e}")
            return None
        except Exception as e:
            logger.error(f"处理API响应时出错: {e}")
            return None

    def save_analysis_result(self, analysis_result: str, images_data: List[Dict]) -> str:
        """
        保存分析结果到文件

        Args:
            analysis_result: API返回的分析结果
            images_data: 图片数据列表

        Returns:
            保存的文件路径
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = os.path.join(OUTPUT_DIR, f"keyframe_analysis_{timestamp}.json")

            # 构建完整的结果数据
            result_data = {
                "analysis_timestamp": timestamp,
                "total_images": len(images_data),
                "images_info": [
                    {
                        "scene_number": img['scene_number'],
                        "filename": img['filename'],
                        "file_path": img['file_path']
                    }
                    for img in images_data
                ],
                "api_response": analysis_result,
                "api_config": {
                    "url": GEMINI_BALANCE_API_URL,
                    "model": GEMINI_BALANCE_MODEL
                }
            }

            # 保存到文件
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result_data, f, ensure_ascii=False, indent=2)

            logger.info(f"分析结果已保存到: {output_file}")
            return output_file

        except Exception as e:
            logger.error(f"保存分析结果时出错: {e}")
            return ""


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='视频关键帧分析工具')
    parser.add_argument('--test', action='store_true', help='测试模式，只分析前2张图片')
    parser.add_argument('--count', type=int, help='指定要分析的图片数量')

    args = parser.parse_args()

    # 确定要处理的图片数量
    if args.test:
        count = 2
        logger.info("运行测试模式，只分析前2张图片")
    elif args.count:
        count = args.count
        logger.info(f"指定分析图片数量: {count}")
    else:
        count = None
        logger.info("分析所有图片")

    # 创建分析器实例
    analyzer = KeyframeAnalyzer()

    # 扫描图片文件
    image_files = analyzer.scan_keyframes_directory()
    if not image_files:
        logger.error("未找到任何图片文件，程序退出")
        return

    # 按scene编号排序
    scene_files = analyzer.sort_images_by_scene(image_files)
    if not scene_files:
        logger.error("排序失败，程序退出")
        return

    # 准备图片数据
    images_data = analyzer.prepare_images_for_analysis(scene_files, count)
    if not images_data:
        logger.error("准备图片数据失败，程序退出")
        return

    logger.info("图片处理和排序功能测试完成")
    logger.info(f"准备分析的图片:")
    for img in images_data:
        logger.info(f"  - Scene {img['scene_number']:03d}: {img['filename']}")

    # 调用API分析图片
    logger.info("\n开始调用Gemini Balance API分析图片...")
    analysis_result = analyzer.call_gemini_balance_api(images_data)

    if analysis_result:
        logger.info("API分析完成")

        # 保存结果
        output_file = analyzer.save_analysis_result(analysis_result, images_data)
        if output_file:
            logger.info(f"分析完成！结果已保存到: {output_file}")

            # 显示部分结果预览
            logger.info("\n分析结果预览:")
            logger.info("=" * 50)
            preview = analysis_result[:500] + "..." if len(analysis_result) > 500 else analysis_result
            logger.info(preview)
            logger.info("=" * 50)
        else:
            logger.error("保存结果失败")
    else:
        logger.error("API分析失败")


if __name__ == "__main__":
    main()